# Sync Scoping Overview

This document summarises how the MakeAgent codebase manages scoped data syncs when a user connects a provider and workflows require specific scopes.

## Connection Flow

When a user connects a provider from the UI, the `Connection` component starts syncs with optional metadata describing the scope. Each entry is sent via `connectionSyncsStore.start`:

```tsx
// packages/ma-next/src/components/setup/Connection.tsx
if (requiredSyncs) {
  Object.entries(requiredSyncs).forEach(([name, scope]) => {
    connectionSyncsStore.start(provider, { name, metadata: scope });
  });
}
```

`connectionSyncsStore.start` forwards the request to the `connection-syncs` Netlify function, optimistically updating UI state and triggering Nango:

```ts
// packages/ma-next/src/stores/connectionSyncs.ts
await invokeFunction('connection-syncs', {
  method: 'POST',
  body: { providerKey, syncs: payload, action: 'start' },
});
```

On the server, the function updates metadata and starts the sync:

```ts
// packages/ma-next/netlify/functions/connection-syncs/index.mts
for (const [name, meta] of Object.entries(syncs)) {
  if (meta && Object.keys(meta).length) {
    await nango.updateMetadata(providerKey, connection.id, { [name]: meta });
  }
  await nango.startSync(providerKey, [name], connection.id);
}
```

## Workflow Extraction

During taskflow extraction, `processTaskflowJson` looks for `syncTrigger` nodes in the workflow definition. Any scopes declared in the workflow are merged into the user's connection metadata using `mergeSyncScopes`:

```ts
// packages/ma-next/netlify/functions/_taskflow-extraction/processTaskflowJson.ts
const scope = node.parameters?.syncScopes || null;
...
await mergeSyncScopes(provider, scopes, user.id, supabase, nango);
```

`mergeSyncScopes` fetches existing metadata from Nango, merges the new values and starts the relevant syncs:

```ts
// packages/ma-next/netlify/functions/_taskflow-extraction/mergeSyncScopes.ts
metadata = (await nango.getMetadata(providerKey, connection.id)) || {};
for (const [name, scope] of Object.entries(scopes)) {
  const merged = mergeValues(metadata[name], scope);
  metadata[name] = merged;
  await nango.updateMetadata(providerKey, connection.id, { [name]: merged });
  await nango.startSync(providerKey, [name], connection.id);
}
```

Sync trigger nodes are persisted in the `sync_triggers` table with the scope stored as JSON:

```ts
// packages/supabase/schema.prisma
model SyncTrigger {
  ...
  syncScope   Json?
}
```

## Webhook Processing

When Nango sends sync webhooks, `handleDataSync` retrieves matching triggers and loads scoped records:

```ts
// packages/ma-next/netlify/functions/sync/handleDataSync.ts
const { data: syncTriggers } = await supabase
  .from('sync_triggers')
  .select(...)
  .eq('providerKey', body.providerConfigKey)
  .eq('model', body.model);
...
const result = await nango.listRecords({
  providerConfigKey: body.providerConfigKey,
  connectionId: body.connectionId,
  model: body.model,
  modifiedAfter: body.modifiedAfter,
});
```

Records are filtered with `matchCondition` and each matching record triggers the taskflow execution.

## Using Metadata in Syncs

Sync implementations pull this metadata to limit API calls. For example, the Google Drive `documents-fork` sync reads folders and files from the metadata:

```ts
// packages/emcpe-nango-integrations/google-drive/google-drive/syncs/documents-fork.ts
const metadata = (await nango.getMetadata<GoogleScopeSyncMetadata>()) || {};
const initialFolders =
  metadata.folders && metadata.folders.length > 0 ? [...metadata.folders] : ['root'];
```

## Flow Diagram

```mermaid
flowchart TD
    A(Workflow JSON) --> B(processTaskflowJson)
    B --> C{Connection exists?}
    C -- yes --> D(mergeSyncScopes)
    D --> E(Nango metadata + startSync)
    C -- no --> F(Setup step: connectProvider)
    G(Connection UI) --> H(connectionSyncsStore.start)
    H --> I(connection-syncs function)
    I --> E
    E --> J(Nango)
    J --> K(Nango Webhook)
    K --> L(handleDataSync)
    L --> M(triggerTaskFlow)
```

This shows the lifecycle from workflow extraction through connection setup, metadata updates, and webhook-driven taskflow execution.

