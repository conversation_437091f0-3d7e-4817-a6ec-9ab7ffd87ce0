import type { NangoAction } from '../../models';
import { GoogleCalendarEvent, GoogleCalendarEventUpdateInput } from '../../models';

type NangoError = { error: { status: number; message: string } };

export default async function updateEvent(
  nango: NangoAction,
  input: GoogleCalendarEventUpdateInput
): Promise<GoogleCalendarEvent | NangoError> {
  const { calendarId, eventId, sendUpdates, start, end, timeZone, ...eventData } = input;

  const startDateTime = start ? { dateTime: start, timeZone: timeZone } : undefined;
  const endDateTime = end ? { dateTime: end, timeZone: timeZone } : undefined;

  const endpoint = `/calendar/v3/calendars/${encodeURIComponent(calendarId)}/events/${encodeURIComponent(
    eventId
  )}`;

  const queryParams: Record<string, string | number> = {};
  if (sendUpdates !== undefined) {
    queryParams['sendUpdates'] = sendUpdates;
  }

  try {
    const eventResponse = await nango.proxy({
      method: 'PATCH',
      endpoint: endpoint,
      providerConfigKey: 'google-calendar',
      connectionId: nango.connectionId,
      params: queryParams as any,
      data: {
        ...eventData,
        ...(startDateTime && { start: startDateTime }),
        ...(endDateTime && { end: endDateTime }),
      },
      retries: 3,
    });

    return eventResponse.data as GoogleCalendarEvent;
  } catch (error: any) {
    const status = error.response?.status ?? 500;
    const message =
      error.response?.data?.error?.message ||
      error.message ||
      'Failed to update Google Calendar event';
    console.error('Error updating event:', error);
    return { error: { status: status, message: message } };
  }
}
