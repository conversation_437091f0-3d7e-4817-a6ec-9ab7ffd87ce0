import type { NangoAction, GmailSendEmailOutput, GmailSendEmailInput } from '../../models';
import { buildAttachmentLines } from '../utils/attachmentHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailSendEmailInput
): Promise<GmailSendEmailOutput | NangoError> {
  try {
    const { to, subject, body, from, cc, bcc, attachments } = input;

    if (!to || !/@/.test(to)) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Invalid or missing recipient email address',
        },
      };
    }

    // Handle content based on attachments
    let lines: string[];

    if (!attachments || attachments.length === 0) {
      // Simple email without attachments
      lines = [
        `To: ${to}`,
        `Subject: ${subject}`,
        'MIME-Version: 1.0',
        'Content-Type: text/plain; charset=UTF-8',
        ...(from ? [`From: ${from}`] : []),
        ...(cc ? [`Cc: ${cc}`] : []),
        ...(bcc ? [`Bcc: ${bcc}`] : []),
        '',
        body || '',
      ];
    } else {
      // Email with attachments
      const boundary = '----=_Part_' + Math.random().toString(36).slice(2);
      lines = [
        `To: ${to}`,
        `Subject: ${subject}`,
        'MIME-Version: 1.0',
        `Content-Type: multipart/mixed; boundary="${boundary}"`,
        ...(from ? [`From: ${from}`] : []),
        ...(cc ? [`Cc: ${cc}`] : []),
        ...(bcc ? [`Bcc: ${bcc}`] : []),
        '',
        `--${boundary}`,
        'Content-Type: text/plain; charset="UTF-8"',
        '',
        body || '',
      ];

      const attachmentLines = await buildAttachmentLines(nango, attachments, boundary);
      lines.push(...attachmentLines);
      lines.push(`--${boundary}--`);
    }

    // Construct and encode email
    const email = lines.join('\n');
    const base64EncodedEmail = Buffer.from(email)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    // Send email via API
    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/gmail/v1/users/me/messages/send',
      data: { raw: base64EncodedEmail },
      retries: 3,
    });

    return {
      id: response.data.id,
      threadId: response.data.threadId,
      labelIds: response.data.labelIds || [],
    };
  } catch (error: any) {
    console.error('Error sending email:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while sending the email.';
    return { error: { status, message } };
  }
}
