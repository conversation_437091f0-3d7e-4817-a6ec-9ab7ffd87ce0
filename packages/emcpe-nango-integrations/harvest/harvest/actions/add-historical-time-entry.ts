import type {
  NangoAction,
  HarvestAddHistoricalTimeEntryInput,
  HarvestCompany,
  HarvestTimeEntry,
} from '../../models';
import { getHarvestAccountId } from '../utils/harvestHelpers';
const METADATA_KEY = 'harvestCompanySettings';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

async function getCompanySettings(nango: NangoAction): Promise<HarvestCompany | NangoError> {
  try {
    const cachedSettings = (await nango.getMetadata()) as { [METADATA_KEY]?: HarvestCompany };
    if (cachedSettings && cachedSettings[METADATA_KEY]) {
      return cachedSettings[METADATA_KEY];
    }
  } catch (error: any) {
    console.warn('Failed to retrieve metadata, fetching from API:', error);
  }

  const accountId = await getHarvestAccountId(nango);
  if (typeof accountId !== 'string') {
    return accountId as NangoError;
  }
  const response = await nango.proxy({
    endpoint: '/v2/company',
    method: 'GET',
    headers: { 'Harvest-Account-Id': accountId },
    retries: 2,
  });

  if (response.status !== 200 || !response.data) {
    return {
      error: {
        status: response.status,
        message: `FETCH_COMPANY_FAILED: Failed to fetch company settings. Status: ${response.status}. Response: ${JSON.stringify(response.data)}`,
      },
    };
  }

  const companyData = response.data as HarvestCompany;

  try {
    await nango.updateMetadata({ [METADATA_KEY]: companyData });
  } catch (error: any) {
    console.error('Failed to set metadata:', error);
  }

  return companyData;
}

export default async function runAction(
  nango: NangoAction,
  input: HarvestAddHistoricalTimeEntryInput
): Promise<HarvestTimeEntry | NangoError> {
  try {
    const accountId = await getHarvestAccountId(nango);
    if (typeof accountId !== 'string') {
      return accountId as NangoError;
    }
    const companySettings = await getCompanySettings(nango);

    if ((companySettings as NangoError).error) {
      return companySettings as NangoError;
    }

    const wantsTimestampTimers = (companySettings as HarvestCompany).wants_timestamp_timers;

    let payload: Record<string, any> = {
      project_id: input.project_id,
      task_id: input.task_id,
      spent_date: input.spent_date,
      notes: input.notes,
      user_id: input.user_id,
      external_reference: input.external_reference,
    };

    if (wantsTimestampTimers) {
      if (!input.started_time || !input.ended_time) {
        return {
          error: {
            status: 400,
            message:
              'MISSING_TIMESTAMP_INPUT: Company uses timestamp tracking. `started_time` and `ended_time` are required for historical entries.',
          },
        };
      }
      if (input.hours !== undefined) {
        return {
          error: {
            status: 400,
            message:
              'INVALID_TIMESTAMP_INPUT: Company uses timestamp tracking. `hours` should not be provided; use `started_time` and `ended_time` instead.',
          },
        };
      }
      payload = {
        ...payload,
        started_time: input.started_time,
        ended_time: input.ended_time,
      };
    } else {
      if (input.hours === undefined) {
        return {
          error: {
            status: 400,
            message:
              'MISSING_DURATION_INPUT: Company uses duration tracking. `hours` is required for historical entries.',
          },
        };
      }
      if (input.started_time !== undefined || input.ended_time !== undefined) {
        return {
          error: {
            status: 400,
            message:
              'INVALID_DURATION_INPUT: Company uses duration tracking. `started_time` and `ended_time` should not be provided; use `hours` instead.',
          },
        };
      }
      payload = {
        ...payload,
        hours: input.hours,
      };
    }

    Object.keys(payload).forEach(key => payload[key] === undefined && delete payload[key]);

    const response = await nango.proxy({
      endpoint: '/v2/time_entries',
      method: 'POST',
      data: payload,
      headers: {
        'Harvest-Account-Id': accountId,
      },
      retries: 2,
    });

    if (response.status !== 201 || !response.data) {
      return {
        error: {
          status: response.status,
          message: `CREATE_TIME_ENTRY_FAILED: Failed to create time entry. Status: ${response.status}. Response: ${JSON.stringify(response.data)}`,
        },
      };
    }

    return response.data as HarvestTimeEntry;
  } catch (error: any) {
    console.error('Error adding historical time entry:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while adding the historical time entry.';
    return { error: { status, message } };
  }
}
