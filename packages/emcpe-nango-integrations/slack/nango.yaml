integrations:
    slack:
        syncs:
            messages:
                runs: every 1 minutes
                description: |
                    Syncs messages from all channels the user can access, including replies.
                    Cursors are stored per channel in metadata for incremental syncing.
                version: 1.0.0
                auto_start: false
                scopes:
                    - channels:read
                    - groups:read
                    - im:read
                    - mpim:read
                    - channels:history
                    - groups:history
                    - im:history
                    - mpim:history
                input: SlackScopeSyncMetadata
                output: SlackSyncMessage
                sync_type: incremental
                endpoint:
                    method: GET
                    path: /messages
                    group: Messages
        actions:
            send-message-as-user: # Keep _as_user suffix
                endpoint: POST /chat.postMessage
                description: Sends a message to a Slack channel as the authenticated user.
                input: SlackSendMessageInput
                output: SlackSendMessageOutput
                scopes:
                    - chat:write
            list-channels: # Keep original name
                endpoint: GET /conversations.list
                description: Lists channels in Slack.
                input: SlackListChannelsInput
                output: SlackConversationsList
                scopes:
                    - channels:read
                    - groups:read
                    - im:read
                    - mpim:read
            get-channel-history: # Keep original name
                endpoint: GET /conversations.history
                description: Retrieves message history for a specific channel.
                input: SlackGetChannelHistoryInput
                output: SlackMessageList
                scopes:
                    - channels:history
                    - groups:history
                    - im:history
                    - mpim:history
            get-user-info: # Keep original name
                endpoint: GET /users.info
                description: Retrieves information about a specific user.
                input: SlackGetUserInfoInput
                output: SlackUserInfo
                scopes:
                    - users:read
            add-reaction-as-user: # Keep _as_user suffix
                endpoint: POST /reactions.add
                description: Adds an emoji reaction to a message as the authenticated user.
                input: SlackAddReactionInput
                output: SlackReactionOutput
                scopes:
                    - reactions:write
            search-messages: # Keep original name
                endpoint: GET /search.messages
                description: Searches for messages matching a query.
                input: SlackSearchMessagesInput
                output: SlackSearchResultList
                scopes:
                    - search:read # Requires search:read scope
            get-message-permalink: # Keep original name
                endpoint: GET /chat.getPermalink
                description: Retrieves a permalink for a specific message.
                input: SlackGetPermalinkInput
                output: SlackPermalinkOutput
                scopes:
                    - chat:read # Likely already included in user scopes
            update-message-as-user: # Keep _as_user suffix
                endpoint: POST /chat.update
                description: Updates an existing message in a channel as the authenticated user.
                input: SlackUpdateMessageInput
                output: SlackUpdateMessageOutput
                scopes:
                    - chat:write # Requires chat:write scope
            delete-message-as-user: # Keep _as_user suffix
                endpoint: POST /chat.delete
                description: Deletes a message from a channel as the authenticated user.
                input: SlackDeleteMessageInput
                output: SlackDeleteMessageOutput
                scopes:
                    - chat:write # Requires chat:write scope

models:
    # Sync-related models
    SlackSyncMessage:
          id: string              # Unique ID for the message (can be the message's 'ts')
          channel_id: string      # ID of the channel
          message: SlackMessage   # Message details
    # Slack Models
    SlackSendMessageInput:
        channel: string         # Channel ID to send the message to
        text: string            # Text of the message to send
        thread_ts?: string      # Optional timestamp of a message to reply to (starts a thread)

    SlackSendMessageOutput:
        ok: boolean             # Indicates success
        ts: string              # Timestamp of the message sent (acts as message ID)
        channel: string         # Channel ID where the message was sent
        message_text?: string   # The text of the message that was sent (Made optional)

    SlackListChannelsInput:
        types?: string          # Optional comma-separated list of conversation types (public_channel, private_channel, mpim, im)
        limit?: number          # Optional maximum number of channels to return
        cursor?: string         # Optional pagination cursor

    SlackScopeSyncMetadata:
        conversations?: string[]

    SlackConversation:
        id: string
        name?: string
        is_channel?: boolean
        is_group?: boolean
        is_im?: boolean
        is_mpim?: boolean
        is_private?: boolean
        is_member?: boolean
        user?: string
        num_members?: number
        # ... additional fields omitted

    SlackConversationsList:
        ok: boolean
        channels: SlackConversation[]
        response_metadata?: SlackResponseMetadata
        error?: string

    SlackGetChannelHistoryInput:
        channel: string         # Channel ID to fetch history from
        limit?: number          # Optional maximum number of messages to return (default 100)
        latest?: string         # Optional end of time range (timestamp)
        oldest?: string         # Optional start of time range (timestamp)
        cursor?: string         # Optional pagination cursor for more messages

    SlackMessage:
        type: string            # Message type (usually 'message')
        subtype?: string        # Message subtype (e.g., 'bot_message', 'file_share')
        ts: string              # Timestamp of the message (acts as ID)
        user?: string           # User ID who sent the message (if applicable)
        text: string            # Message text content
        thread_ts?: string      # Timestamp of the parent message if this is part of a thread
        reply_count?: number    # Number of replies in the thread (if it's a parent message)
        blocks?: SlackBlock[]   # Rich message layout blocks
        attachments?: SlackAttachment[] # Attachments (legacy)
        files?: SlackFile[]     # Files shared in the message
        reactions?: SlackReaction[] # Reactions to the message
        parent_user_id?: string # Parent user ID if this is a thread reply
        edited?: SlackEdited    # Edit info if the message was edited
        bot_id?: string         # Bot ID if sent by a bot
        icons?: object          # Bot/user icons (optional, can be object)
        team?: string           # Team ID
        app_id?: string         # App ID if sent by an app
        client_msg_id?: string  # Client message ID
        # Add more fields as needed for full fidelity

    SlackBlock:
        type: string
        block_id?: string
        text?: object
        elements?: object[]
        fields?: object[]
        accessory?: object

    SlackAttachment:
        id?: number
        fallback?: string
        color?: string
        pretext?: string
        author_name?: string
        author_link?: string
        author_icon?: string
        title?: string
        title_link?: string
        text?: string
        fields?: object[]
        image_url?: string
        thumb_url?: string
        footer?: string
        footer_icon?: string
        ts?: number

    SlackFile:
        id: string
        name?: string
        filetype?: string
        url_private?: string
        url_private_download?: string
        mimetype?: string
        size?: number
        title?: string
        created?: number
        timestamp?: number
        user?: string
        editable?: boolean
        mode?: string
        is_external?: boolean
        external_type?: string
        permalink?: string
        preview?: string
        accessible?: UrlAccessibleFile
        # Add more fields as needed

    SlackReaction:
        name: string
        count: number
        users: string[]

    SlackEdited:
        user: string
        ts: string

    SlackResponseMetadata:
        next_cursor?: string

    SlackMessageList:
        ok: boolean                 # Indicates success
        messages: SlackMessage[]    # List of messages
        has_more?: boolean          # Indicates if there are more messages
        pin_count?: number          # Number of pinned items in the channel
        channel_actions_ts?: string # Timestamp of channel actions (optional)
        channel_actions_count?: number # Count of channel actions (optional)
        response_metadata?: SlackResponseMetadata # Pagination info
        error?: string              # Error message if not ok

    SlackGetUserInfoInput:
        user: string            # User ID to fetch information for

    SlackUserProfile:
        real_name?: string      # User's real name
        display_name?: string   # User's display name
        email?: string          # User's email (requires users:read.email scope)
        image_original?: string # URL to original profile image
        image_512?: string      # URL to 512x512 profile image
        # Add other profile fields if needed

    SlackUserInfo:
        id: string              # User ID
        name: string            # Username
        is_bot: boolean         # Is the user a bot?
        is_admin?: boolean      # Is the user an admin? (Optional)
        is_owner?: boolean      # Is the user an owner? (Optional)
        tz?: string             # Timezone identifier
        profile?: SlackUserProfile # User profile details

    SlackAddReactionInput:
        name: string            # Emoji name (e.g., "thumbsup", "heart")
        channel: string         # Channel ID containing the message
        timestamp: string       # Timestamp of the message to react to (the 'ts' value)

    SlackReactionOutput:
        ok: boolean             # Indicates success
        error?: string          # Error message if not ok

    SlackSearchMessagesInput:
        query: string           # Search query
        sort?: string           # Sort direction (score or timestamp)
        sort_dir?: string       # Sort direction (asc or desc)
        count?: number          # Number of items to return per page (default 20)
        page?: number           # Page number to return

    SlackSearchMatch:
        iid: string             # Item ID
        team: string            # Team ID
        channel: object         # Channel object { id, name, is_private, ... }
        type: string            # Type of item (e.g., 'message')
        user: string            # User ID of the sender
        username: string        # Username of the sender
        ts: string              # Timestamp of the message
        text: string            # Message text
        permalink: string       # Permanent link to the message

    SlackSearchResultList:
        ok: boolean             # Indicates success
        query: string           # The query string used
        messages: object        # Contains matches, pagination info { matches: SlackSearchMatch[], pagination: object }
        error?: string          # Error message if not ok

    SlackGetPermalinkInput:
        channel: string         # Channel ID containing the message
        message_ts: string      # Timestamp of the message ('ts' value)

    SlackPermalinkOutput:
        ok: boolean             # Indicates success
        permalink?: string      # The retrieved permalink URL
        channel?: string        # Channel ID (returned by API)
        error?: string          # Error message if not ok

    SlackUpdateMessageInput:
        channel: string         # Channel ID containing the message
        ts: string              # Timestamp of the message to update
        text: string            # New text for the message
        # Optional fields like blocks, parse, link_names etc. can be added

    SlackUpdateMessageOutput:
        ok: boolean             # Indicates success
        channel?: string        # Channel ID
        ts?: string             # Timestamp of the updated message
        text?: string           # Updated text content
        error?: string          # Error message if not ok

    UrlAuthentication:
        providerKey: string
        connectionId: string

    UrlAccessibleFile:
        url: string
        authentication: UrlAuthentication

    SlackDeleteMessageInput:
        channel: string         # Channel ID containing the message
        ts: string              # Timestamp of the message to delete

    SlackDeleteMessageOutput:
        ok: boolean             # Indicates success
        channel?: string        # Channel ID
        ts?: string             # Timestamp of the deleted message
        error?: string          # Error message if not ok
