import type { NangoSync, TwitterMention } from '../../models';

export default async function fetchData(nango: NangoSync): Promise<void> {
  // Retrieve stored metadata from previous runs
  const metadata = await nango.getMetadata<{
    userId?: string;
    username?: string;
    newestMentionId?: string;
    oldestMentionId?: string;
  }>();

  let userId = metadata?.userId;

  // If we don't have the user ID yet, get the authenticated user's info
  if (!userId) {
    try {
      // Get the authenticated user's information
      const userResponse = await nango.proxy({
        method: 'GET',
        endpoint: '/2/users/me',
        params: {
          'user.fields': 'id,name,username',
        },
      });

      userId = userResponse.data.data.id;
      const username = userResponse.data.data.username;

      // Store the user ID and username in metadata for future runs
      await nango.updateMetadata({
        userId,
        username,
      });

      console.log(`Fetching mentions for authenticated user: @${username} (ID: ${userId})`);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message;
      console.error(`Error getting authenticated user: ${errorMessage}`);
      throw new Error(`Failed to get authenticated user info: ${errorMessage}`);
    }
  } else {
    console.log(`Using stored user ID: ${userId} (${metadata.username || 'unknown username'})`);
  }

  // Prepare parameters for fetching mentions
  const mentionsParams: Record<string, any> = {
    max_results: 10,
    'tweet.fields':
      'id,text,created_at,author_id,conversation_id,in_reply_to_user_id,referenced_tweets,public_metrics',
    expansions: 'referenced_tweets.id,author_id',
    'user.fields': 'id,name,username',
  };

  // For incremental syncs, use since_id if we have it
  if (metadata?.newestMentionId) {
    mentionsParams['since_id'] = metadata.newestMentionId;
  }

  try {
    // Fetch mentions of the user
    const mentionsResponse = await nango.proxy({
      method: 'GET',
      endpoint: `/2/users/${userId}/mentions`,
      params: mentionsParams,
    });

    const mentions = mentionsResponse.data.data || [];

    // Process and map mentions to our model
    const processedMentions: TwitterMention[] = mentions.map((mention: any) => {
      // Process referenced tweets if any
      const referencedTweets = mention.referenced_tweets?.map((ref: any) => {
        return {
          type: ref.type,
          id: ref.id,
        };
      });

      return {
        id: mention.id,
        text: mention.text,
        created_at: mention.created_at,
        author_id: mention.author_id,
        conversation_id: mention.conversation_id,
        in_reply_to_user_id: mention.in_reply_to_user_id,
        referenced_tweets: referencedTweets,
        public_metrics: {
          retweet_count: mention.public_metrics?.retweet_count || 0,
          reply_count: mention.public_metrics?.reply_count || 0,
          like_count: mention.public_metrics?.like_count || 0,
          quote_count: mention.public_metrics?.quote_count || 0,
        },
        // Add mention details
        mention: {
          username: metadata.username || '',
          id: userId || '',
        },
      };
    });

    // Save the mentions
    if (processedMentions.length > 0) {
      await nango.batchSave(processedMentions, 'TwitterMention');
      console.log(`Saved ${processedMentions.length} mentions`);

      // Update metadata with the newest and oldest mention IDs for pagination
      const mentionIds = processedMentions.map(mention => mention.id);

      // Sort mention IDs numerically (they're strings but can be compared)
      mentionIds.sort();

      const oldestMentionId = mentionIds[0];
      const newestMentionId = mentionIds[mentionIds.length - 1];

      await nango.updateMetadata({
        userId,
        username: metadata.username,
        newestMentionId: newestMentionId || metadata?.newestMentionId,
        oldestMentionId: oldestMentionId || metadata?.oldestMentionId,
      });
    } else {
      console.log('No new mentions found');
      // No new mentions, just ensure userId is saved
      await nango.updateMetadata({
        userId,
        username: metadata.username,
        newestMentionId: metadata?.newestMentionId,
        oldestMentionId: metadata?.oldestMentionId,
      });
    }
  } catch (error: any) {
    const errorMessage = error.response?.data?.detail || error.message;
    console.error(`Error fetching mentions: ${errorMessage}`);

    if (error.response?.status === 429) {
      console.log('Rate limit exceeded. Try again later.');
      return;
    }

    throw new Error(`Failed to fetch mentions: ${errorMessage}`);
  }
}
