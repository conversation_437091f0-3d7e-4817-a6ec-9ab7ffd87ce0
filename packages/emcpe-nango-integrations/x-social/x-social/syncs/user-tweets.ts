import type { NangoSync, TwitterPost } from '../../models';

export default async function fetchData(nango: NangoSync): Promise<void> {
  // Retrieve stored metadata from previous runs
  const metadata = await nango.getMetadata<{
    userId?: string;
    username?: string;
    newestTweetId?: string;
    oldestTweetId?: string;
  }>();

  let userId = metadata?.userId;

  // If we don't have the user ID yet, get the authenticated user's info
  if (!userId) {
    try {
      // Get the authenticated user's information
      const userResponse = await nango.proxy({
        method: 'GET',
        endpoint: '/2/users/me',
        params: {
          'user.fields': 'id,name,username',
        },
      });

      userId = userResponse.data.data.id;
      const username = userResponse.data.data.username;

      // Store the user ID and username in metadata for future runs
      await nango.updateMetadata({
        userId,
        username,
      });

      console.log(`Fetching tweets for authenticated user: @${username} (ID: ${userId})`);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message;
      console.error(`Error getting authenticated user: ${errorMessage}`);
      throw new Error(`Failed to get authenticated user info: ${errorMessage}`);
    }
  } else {
    console.log(`Using stored user ID: ${userId} (${metadata.username || 'unknown username'})`);
  }

  // Prepare parameters for fetching tweets
  const tweetParams: Record<string, any> = {
    max_results: 10,
    'tweet.fields':
      'id,text,created_at,author_id,conversation_id,in_reply_to_user_id,referenced_tweets,public_metrics',
    expansions: 'referenced_tweets.id',
  };

  // For incremental syncs, use since_id if we have it
  if (metadata?.newestTweetId) {
    tweetParams['since_id'] = metadata.newestTweetId;
  }

  try {
    // Fetch tweets from the user's timeline
    const tweetsResponse = await nango.proxy({
      method: 'GET',
      endpoint: `/2/users/${userId}/tweets`,
      params: tweetParams,
    });

    const tweets = tweetsResponse.data.data || [];

    // Process and map tweets to our model
    const processedTweets: TwitterPost[] = tweets.map((tweet: any) => {
      // Process referenced tweets if any
      const referencedTweets = tweet.referenced_tweets?.map((ref: any) => {
        return {
          type: ref.type,
          id: ref.id,
        };
      });

      return {
        id: tweet.id,
        text: tweet.text,
        created_at: tweet.created_at,
        author_id: tweet.author_id,
        conversation_id: tweet.conversation_id,
        in_reply_to_user_id: tweet.in_reply_to_user_id,
        referenced_tweets: referencedTweets,
        public_metrics: {
          retweet_count: tweet.public_metrics?.retweet_count || 0,
          reply_count: tweet.public_metrics?.reply_count || 0,
          like_count: tweet.public_metrics?.like_count || 0,
          quote_count: tweet.public_metrics?.quote_count || 0,
        },
      };
    });

    // Save the tweets
    if (processedTweets.length > 0) {
      await nango.batchSave(processedTweets, 'TwitterPost');
      console.log(`Saved ${processedTweets.length} tweets`);

      // Update metadata with the newest and oldest tweet IDs for pagination
      const tweetIds = processedTweets.map(tweet => tweet.id);

      // Sort tweet IDs numerically (they're strings but can be compared)
      tweetIds.sort();

      const oldestTweetId = tweetIds[0];
      const newestTweetId = tweetIds[tweetIds.length - 1];

      await nango.updateMetadata({
        userId,
        username: metadata.username,
        newestTweetId: newestTweetId || metadata?.newestTweetId,
        oldestTweetId: oldestTweetId || metadata?.oldestTweetId,
      });
    } else {
      console.log('No new tweets found');
      // No new tweets, just ensure userId is saved
      await nango.updateMetadata({
        userId,
        username: metadata.username,
        newestTweetId: metadata?.newestTweetId,
        oldestTweetId: metadata?.oldestTweetId,
      });
    }
  } catch (error: any) {
    const errorMessage = error.response?.data?.detail || error.message;
    console.error(`Error fetching tweets: ${errorMessage}`);

    if (error.response?.status === 429) {
      console.log('Rate limit exceeded. Try again later.');
      return;
    }

    throw new Error(`Failed to fetch tweets: ${errorMessage}`);
  }
}
