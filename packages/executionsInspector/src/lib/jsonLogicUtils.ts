// Simple local implementation of matchCondition for testing purposes
// This avoids complex cross-package imports during development

export function matchCondition(record: any, condition: any): boolean {
  // If no condition, always match
  if (!condition || typeof condition !== 'object') {
    return true;
  }

  // Simple implementation for basic JSONLogic operations
  if (condition['!=']) {
    const [left, right] = condition['!='];
    const leftVal = getValueFromRecord(record, left);
    const rightVal = getValueFromRecord(record, right);
    return leftVal !== rightVal;
  }

  if (condition['==']) {
    const [left, right] = condition['=='];
    const leftVal = getValueFromRecord(record, left);
    const rightVal = getValueFromRecord(record, right);
    return leftVal === rightVal;
  }

  if (condition['>']) {
    const [left, right] = condition['>'];
    const leftVal = getValueFromRecord(record, left);
    const rightVal = getValueFromRecord(record, right);
    return leftVal > rightVal;
  }

  if (condition['<']) {
    const [left, right] = condition['<'];
    const leftVal = getValueFromRecord(record, left);
    const rightVal = getValueFromRecord(record, right);
    return leftVal < rightVal;
  }

  if (condition['and']) {
    return condition['and'].every((cond: any) => matchCondition(record, cond));
  }

  if (condition['or']) {
    return condition['or'].some((cond: any) => matchCondition(record, cond));
  }

  // Default: assume match for any other condition
  return true;
}

/**
 * Simple parameter evaluation for testing purposes
 * Resolves templates like {{trigger.value}} using the provided input data
 */
export function evaluateNodeParameters(
  parameters: Record<string, any>,
  input: Record<string, any>,
  tournament?: any // Optional tournament parameter for compatibility
): Record<string, any> {
  if (Array.isArray(parameters)) {
    return parameters.map(item => handleValue(item, input));
  }

  const resolved: Record<string, any> = {};
  for (const [key, value] of Object.entries(parameters)) {
    resolved[key] = handleValue(value, input);
  }
  return resolved;
}

function handleValue(value: any, input: Record<string, any>) {
  if (typeof value === 'string') {
    // Simple template replacement for patterns like {{key.path}}
    return value.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const resolvedValue = getNestedValue(input, path.trim());
      return resolvedValue !== undefined ? String(resolvedValue) : match;
    });
  } else if (typeof value === 'object' && value !== null) {
    return evaluateNodeParameters(value, input);
  } else {
    return value;
  }
}

function getValueFromRecord(record: any, accessor: any): any {
  if (typeof accessor === 'object' && accessor.var) {
    // Handle {var: "field.name"} accessor
    const path = accessor.var;
    return getNestedValue(record, path);
  }
  
  // Direct value
  return accessor;
}

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}
