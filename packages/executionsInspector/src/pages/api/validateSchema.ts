import type { NextApiRequest, NextApiResponse } from 'next';
import { validateTaskflowSchema } from '../../../../ma-next/netlify/functions/_taskflow_schema_validation/schemaValidator';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const host = req.headers.host;
  if (!host || (!host.includes('localhost') && !host.includes('127.0.0.1'))) {
    return res.status(403).json({ error: 'Access denied' });
  }

  try {
    const { schema } = req.body;
    if (!schema) {
      return res.status(400).json({ error: 'Missing schema' });
    }
    validateTaskflowSchema(schema);
    return res.status(200).json({ success: true });
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Validation failed';
    return res.status(400).json({ success: false, error: message });
  }
}
