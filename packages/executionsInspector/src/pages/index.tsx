import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Profile, Conversation, Taskflow, TaskflowExecution } from '@/types';
import { UserSelector } from '@/components/UserSelector';
import { ConversationSelector } from '@/components/ConversationSelector';
import { TaskflowSelector } from '@/components/TaskflowSelector';
import { ExecutionSelector } from '@/components/ExecutionSelector';
import { SchemaPanel } from '@/components/SchemaPanel';
import { ManualTriggerMode } from '@/components/ManualTriggerMode';
import { ExecutionDetails } from '@/components/ExecutionDetails';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, Activity, Settings } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';
import { useRouter } from 'next/router';

export default function Home() {
  const router = useRouter();

  // Primary data - all executions with joined data
  const [allExecutions, setAllExecutions] = useState<(TaskflowExecution & {
    taskflows: Taskflow & {
      conversations: Conversation & {
        profiles: Profile;
      };
    };
  })[]>([]);

  // Current selections
  const [selectedUser, setSelectedUser] = useState<Profile | null>(null);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [selectedTaskflow, setSelectedTaskflow] = useState<Taskflow | null>(null);
  const [selectedExecution, setSelectedExecution] = useState<TaskflowExecution | null>(null);

  // UI state
  const [mode, setMode] = useState<'trigger' | 'inspection'>('inspection');
  const [isLocalhost, setIsLocalhost] = useState(true);
  const [loading, setLoading] = useState(true);

  // Only allow localhost
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
      setIsLocalhost(isLocal);
    }
  }, []);

  // Fetch all executions with related data on mount
  useEffect(() => {
    async function fetchAllExecutions() {
      setLoading(true);
      try {
        const { data: executions, error } = await supabase
          .from('taskflow_executions')
          .select(`
            *,
            taskflows!taskflow_executions_taskflowId_fkey (
              *,
              conversations!taskflows_conversationId_fkey (
                *,
                profiles!conversations_userId_fkey (
                  id, firstName, lastName, preferences, createdAt, updatedAt
                )
              )
            )
          `)
          .order('updatedAt', { ascending: false });

        if (error) throw error;

        if (executions && executions.length > 0) {
          setAllExecutions(executions as any[]);

          // Auto-select the most recent execution and set up the hierarchy
          const latest = executions[0] as any;
          setSelectedExecution(latest);
          setMode('inspection');

          const taskflow = latest.taskflows;
          const conversation = taskflow?.conversations;
          const profile = conversation?.profiles;

          if (profile) setSelectedUser(profile);
          if (conversation) setSelectedConversation(conversation);
          if (taskflow) setSelectedTaskflow(taskflow);
        }
      } catch (error) {
        console.error('Error fetching executions:', error);
      } finally {
        setLoading(false);
      }
    }
    fetchAllExecutions();
  }, []);

  // When user changes, find their most recent execution and update filters
  const handleUserSelect = (user: Profile | null) => {
    if (!user || !allExecutions.length) {
      setSelectedUser(user);
      setSelectedConversation(null);
      setSelectedTaskflow(null);
      setSelectedExecution(null);
      return;
    }

    const userExecutions = allExecutions.filter(exec =>
      exec.taskflows?.conversations?.profiles?.id === user.id
    );

    if (userExecutions.length > 0) {
      const latest = userExecutions[0];
      setSelectedUser(user);
      setSelectedExecution(latest);
      setMode('inspection');

      const taskflow = latest.taskflows;
      const conversation = taskflow?.conversations;

      setSelectedConversation(conversation || null);
      setSelectedTaskflow(taskflow || null);
    }
  };

  // When conversation changes, find most recent execution for that conversation
  const handleConversationSelect = (conversation: Conversation | null) => {
    if (!conversation || !selectedUser || !allExecutions.length) {
      setSelectedConversation(conversation);
      setSelectedTaskflow(null);
      setSelectedExecution(null);
      return;
    }

    const conversationExecutions = allExecutions.filter(exec =>
      exec.taskflows?.conversations?.id === conversation.id &&
      exec.taskflows?.conversations?.profiles?.id === selectedUser.id
    );

    if (conversationExecutions.length > 0) {
      const latest = conversationExecutions[0];
      setSelectedConversation(conversation);
      setSelectedExecution(latest);
      setMode('inspection');

      const taskflow = latest.taskflows;
      setSelectedTaskflow(taskflow || null);
    }
  };

  // When taskflow changes, find most recent execution for that taskflow
  const handleTaskflowSelect = (taskflow: Taskflow | null) => {
    if (!taskflow || !selectedUser || !selectedConversation || !allExecutions.length) {
      setSelectedTaskflow(taskflow);
      setSelectedExecution(null);
      return;
    }

    const taskflowExecutions = allExecutions.filter(exec =>
      exec.taskflowId === taskflow.id
    );

    if (taskflowExecutions.length > 0) {
      const latest = taskflowExecutions[0];
      setSelectedTaskflow(taskflow);
      setSelectedExecution(latest);
      setMode('inspection');
    }
  };

  const handleTriggerMode = () => {
    setMode('trigger');
    setSelectedExecution(null);
  };

  const handleExecutionSelect = (execution: TaskflowExecution | null) => {
    if (execution) {
      setMode('inspection');
      setSelectedExecution(execution);
    }
  };

  // Get filtered lists for dropdowns
  const getAvailableUsers = (): Profile[] => {
    const userMap = new Map();
    allExecutions.forEach(exec => {
      const profile = exec.taskflows?.conversations?.profiles;
      if (profile) {
        userMap.set(profile.id, profile);
      }
    });
    return Array.from(userMap.values());
  };

  const getAvailableConversations = (): Conversation[] => {
    if (!selectedUser) return [];
    const conversationMap = new Map();
    allExecutions
      .filter(exec => exec.taskflows?.conversations?.profiles?.id === selectedUser.id)
      .forEach(exec => {
        const conversation = exec.taskflows?.conversations;
        if (conversation) {
          conversationMap.set(conversation.id, conversation);
        }
      });
    return Array.from(conversationMap.values());
  };

  const getAvailableTaskflows = (): Taskflow[] => {
    if (!selectedUser || !selectedConversation) return [];
    const taskflowMap = new Map();
    allExecutions
      .filter(exec =>
        exec.taskflows?.conversations?.id === selectedConversation.id &&
        exec.taskflows?.conversations?.profiles?.id === selectedUser.id
      )
      .forEach(exec => {
        const taskflow = exec.taskflows;
        if (taskflow) {
          taskflowMap.set(taskflow.id, taskflow);
        }
      });
    return Array.from(taskflowMap.values());
  };

  const getAvailableExecutions = (): TaskflowExecution[] => {
    if (!selectedUser || !selectedConversation || !selectedTaskflow) return [];
    return allExecutions.filter(exec =>
      exec.taskflowId === selectedTaskflow.id
    );
  };

  if (!isLocalhost) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="flex flex-col items-center text-center py-8">
            <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
            <h1 className="text-xl font-semibold mb-2">Access Restricted</h1>
            <p className="text-muted-foreground">This application is designed to run on localhost only for security reasons.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="flex flex-col items-center text-center py-8">
            <Activity className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Loading Executions</h3>
            <p className="text-muted-foreground">Fetching taskflow execution data...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (allExecutions.length === 0) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="flex flex-col items-center text-center py-8">
            <Activity className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Executions Found</h3>
            <p className="text-muted-foreground">No taskflow executions are available in the database.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/schemas')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <Settings className="h-4 w-4" />
              <span>Schema Tools</span>
            </button>
            <ThemeToggle />
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Taskflow Executions Inspector</span>
          </h1>
          <p className="text-muted-foreground">Debug and trigger taskflow executions locally</p>
          <p className="text-xs text-muted-foreground">
            {allExecutions.length} execution{allExecutions.length !== 1 ? 's' : ''} available
          </p>
        </header>

        {/* Navigation Bar with Selection Dropdowns */}
        <Card>
          <CardContent className="py-4">
            <div className="flex flex-wrap gap-4 items-end justify-center">
              <UserSelector
                availableUsers={getAvailableUsers()}
                selectedUser={selectedUser}
                onUserSelect={handleUserSelect}
              />

              {selectedUser && (
                <>
                  <Separator orientation="vertical" className="h-8" />
                  <ConversationSelector
                    availableConversations={getAvailableConversations()}
                    selectedConversation={selectedConversation}
                    onConversationSelect={handleConversationSelect}
                  />
                </>
              )}

              {selectedConversation && (
                <>
                  <Separator orientation="vertical" className="h-8" />
                  <TaskflowSelector
                    availableTaskflows={getAvailableTaskflows()}
                    selectedTaskflow={selectedTaskflow}
                    onTaskflowSelect={handleTaskflowSelect}
                  />
                </>
              )}

              {selectedTaskflow && (
                <>
                  <Separator orientation="vertical" className="h-8" />
                  <ExecutionSelector
                    executions={getAvailableExecutions()}
                    selectedExecution={selectedExecution}
                    onExecutionSelect={handleExecutionSelect}
                    onTriggerMode={handleTriggerMode}
                  />
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Area */}
        {selectedTaskflow ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Schema Information */}
            <div className="space-y-4">
              <SchemaPanel taskflow={selectedTaskflow} />
            </div>

            {/* Right Column - Execution Details or Manual Trigger */}
            <div className="space-y-4">
              {mode === 'trigger' && selectedUser && (
                <ManualTriggerMode taskflow={selectedTaskflow} userId={selectedUser.id} />
              )}
              {mode === 'inspection' && selectedExecution && (
                <ExecutionDetails executionId={selectedExecution.id} />
              )}
            </div>
          </div>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12 text-center">
              <Activity className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Select a User</h3>
              <p className="text-muted-foreground">
                Choose a user from the dropdown above to view their taskflow executions.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
