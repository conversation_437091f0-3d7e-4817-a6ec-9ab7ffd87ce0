{"nodes": [{"id": "trigger1", "type": "trigger.syncTrigger", "parameters": {"providerKey": "test-provider", "model": "TestModel", "syncKey": "test-sync", "label": "Test Trigger", "description": "A test trigger for validation"}, "condition": {"!=": [{"var": "status"}, "draft"]}}, {"id": "node1", "type": "ai.simple", "parameters": {"system": "You are a test assistant", "prompt": "Process this data: {{trigger.data}}", "outputSchema": {"type": "object", "properties": {"result": {"type": "string", "description": "The processed result"}}, "required": ["result"]}}}, {"id": "node2", "type": "provider.test.action", "parameters": {"input": "{{node1.result}}", "config": {"timeout": 5000, "retries": 3}}}]}