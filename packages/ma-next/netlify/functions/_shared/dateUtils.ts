export function extractDatePaths(records: any[]): string[] {
  const paths = new Set<string>();

  const isDateLike = (key: string, value: any): boolean => {
    const lower = key.toLowerCase();
    if (
      lower.includes('date') ||
      lower.includes('time') ||
      lower.includes('ts') ||
      lower.endsWith('_at')
    ) {
      if (typeof value === 'string') {
        if (!isNaN(Date.parse(value))) return true;
        const num = Number(value);
        if (!isNaN(num) && num > 1e9) return true;
      }
      if (typeof value === 'number' && value > 1e9) return true;
    }
    return false;
  };

  const traverse = (obj: any, prefix: string = '') => {
    if (!obj || typeof obj !== 'object') return;
    for (const [key, val] of Object.entries(obj)) {
      const path = prefix ? `${prefix}.${key}` : key;
      if (isDateLike(key, val)) {
        paths.add(path);
      }
      if (val && typeof val === 'object') {
        traverse(val, path);
      }
    }
  };

  records.forEach(rec => traverse(rec));
  return Array.from(paths);
}

export function getValueAtPath(obj: any, path: string): any {
  return path.split('.').reduce((acc: any, part: string) => (acc ? acc[part] : undefined), obj);
}

export function sortRecordsByPath(
  records: any[],
  path: string,
  order: 'asc' | 'desc' = 'desc'
): any[] {
  const parseDate = (v: any): number => {
    if (v == null) return 0;
    if (typeof v === 'number') {
      if (v > 1e12) return v;
      if (v > 1e9) return v * 1000;
      return v;
    }
    if (typeof v === 'string') {
      const num = Number(v);
      if (!isNaN(num)) {
        if (num > 1e12) return num;
        if (num > 1e9) return num * 1000;
      }
      const d = Date.parse(v);
      if (!isNaN(d)) return d;
    }
    return 0;
  };

  return [...records].sort((a, b) => {
    const av = parseDate(getValueAtPath(a, path));
    const bv = parseDate(getValueAtPath(b, path));
    return order === 'asc' ? av - bv : bv - av;
  });
}
