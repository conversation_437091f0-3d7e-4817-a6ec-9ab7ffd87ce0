import { debug } from './debug';
import { matchCondition } from '../_taskflow/jsonLogicUtils';
import { extractDatePaths, sortRecordsByPath } from './dateUtils';

/**
 * Used by sync & test workflow to filter records based on a condition.
 */
function filterRecords(records: any[], condition: any) {
  if (!condition || !Object.keys(condition).length) {
    return records;
  }
  return records.filter(record => {
    const match = matchCondition(record, condition);
    if (!match) {
      debug(
        `Record ${JSON.stringify(record)} does not match condition ${JSON.stringify(condition)}`
      );
    }
    return match;
  });
}

export { filterRecords };

function heuristicChronologicalSort(records: any[]): any[] {
  if (!Array.isArray(records) || records.length === 0) {
    return records;
  }

  const paths = extractDatePaths(records);
  if (!paths.length) {
    return records;
  }

  const lower = paths.map(p => p.toLowerCase());
  const priorities = [
    'updatedat',
    'lastmodifiedat',
    'modifiedat',
    'start.datetime',
    'starttime',
    'message.ts',
    'timestamp',
    'createdat',
  ];

  let chosen: string | undefined;
  for (const pri of priorities) {
    const idx = lower.findIndex(p => p.includes(pri));
    if (idx !== -1) {
      chosen = paths[idx];
      break;
    }
  }

  if (!chosen) {
    chosen = paths[0];
  }

  return sortRecordsByPath(records, chosen, 'desc');
}

export { heuristicChronologicalSort };
