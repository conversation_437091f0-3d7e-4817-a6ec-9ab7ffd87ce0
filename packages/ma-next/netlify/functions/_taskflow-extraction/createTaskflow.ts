import { SupabaseClient } from '@supabase/supabase-js';
import { createSyncTriggers } from './processSyncTriggers';
import { RawTaskflowTransformer } from './RawTaskflowTransformer';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';
import { debug } from '../_shared/debug';


/**
 * Creates a taskflow in the database
 */
async function createTaskflow(
  workflowSchema: any,
  conversationId: string,
  supabase: SupabaseClient
) {
  const facade = new TaskflowSupabaseFacade(supabase);
  try {
    const transformer = new RawTaskflowTransformer(workflowSchema);
    const { syncTriggerNodes, hasTriggers } = transformer.extractSyncTriggers();

    if (hasTriggers) {
      debug('Extracted sync triggers:', syncTriggerNodes.length);
    }

    // Insert the taskflow with the already processed schema
    const { data: taskflow, error } = await facade.createTaskflow(
      transformer.getSchema(),
      conversationId,
      false
    );

    if (error) {
      console.error('Error creating taskflow:', error);
      return null;
    }

    // Create sync trigger entries if needed
    if (hasTriggers) {
      debug('Creating sync trigger entries for taskflow', taskflow.id);

      try {
        await createSyncTriggers(taskflow.id, syncTriggerNodes, supabase);
      } catch (triggerError) {
        console.error('Error creating sync triggers:', triggerError);
        // We don't return null here because the taskflow was created successfully
        // The sync triggers can be recreated later if needed
      }
    }

    return taskflow;
  } catch (error) {
    console.error('Error in createTaskflow:', error);
    return null;
  }
}

export { createTaskflow };
