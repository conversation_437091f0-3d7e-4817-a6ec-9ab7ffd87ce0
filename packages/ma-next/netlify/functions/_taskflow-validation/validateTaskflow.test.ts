import { test } from 'node:test';
import { strictEqual } from 'node:assert';
import { EMAIL_WORKFLOW_SCHEMA } from '../_agents/sampleWorkflows';
import { validateTaskflow } from './validateTaskflow';
import { NodeHandlers } from '../_taskflow/types';

import dotEnv from 'dotenv';

dotEnv.config();

const handlers: Partial<NodeHandlers> = {
  executeActionNode: async () => ({ status: 'SUCCESS', result: { faked: true } }),
  aiNodeDependencies: {
    createOpenAI: () => ({ responses: () => ({}) }),
    generateObject: async () => ({ object: { subject: 's', body: 'b' } }),
    generateText: async () => ({ text: 'x' }),
    jsonSchema: (s: any) => s,
  } as any,
};

// test('validateTaskflow runs workflow without errors', async () => {
//   const result = await validateTaskflow(
//     EMAIL_WORKFLOW_SCHEMA,
//     {
//       id: '1',
//       sender: '<EMAIL>',
//       recipients: '<EMAIL>',
//       date: '2024-01-01T00:00:00Z',
//       subject: 'Test Subject',
//       body: 'Test Body',
//       attachments: [],
//       threadId: 'thread-1',
//       isDraft: false,
//       labels: ['INBOX', 'IMPORTANT'],
//       snippet: 'Test snippet',
//       cc: '<EMAIL>',
//       bcc: '<EMAIL>',
//       messageId: 'msg-1',
//       inReplyTo: 'msg-0',
//       references: 'msg-0',
//     },
//     {
//       handlers: {
//         ...handlers,
//         aiNodeDependencies: {
//           ...handlers.aiNodeDependencies,
//           generateObject: () => ({
//             object: {
//               subject: 'Re: Test Subject',
//               body: 'Thank you for your email. I will get back to you shortly.\n\nBest regards,\nYour Name',
//             },
//           }),
//         },
//       } as any,
//     }
//   );
//   strictEqual(result, true);
// });

