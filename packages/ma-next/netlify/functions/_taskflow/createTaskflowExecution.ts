import { CreateExecutionRequest } from './types';
import { PreparedData } from './initExecution';
import { DebugTracer } from './debugTracer';

/**
 * Creates a taskflow execution without starting it
 *
 * @param params The request parameters
 * @returns A tuple with the result and error (if any)
 */
async function createTaskflowExecution({
  taskflowId,
  triggerData = {},
  supabaseFacade,
  userId,
  isTest = false,
}: CreateExecutionRequest & { tracer?: DebugTracer; isTest?: boolean }): Promise<
  [PreparedData, null] | [null, Error]
> {
  const facade = supabaseFacade;
  try {
    // Verify the taskflow exists and belongs to the user
    const { data: taskflow, error: taskflowError } = await facade.getUserTaskflow(
      taskflowId,
      userId
    );

    if (taskflowError) {
      return [null, new Error(taskflowError.message || 'Database error when fetching taskflow')];
    }

    if (!taskflow || !taskflow.taskflowSchema) {
      return [null, new Error('Taskflow not found or has no schema')];
    }

    // Create the execution
    const { data: createExecution, error: createExecutionError } = await facade.createExecution(
      taskflowId,
      triggerData,
      isTest
    );

    if (createExecutionError) {
      return [
        null,
        new Error(createExecutionError.message || 'Database error when creating execution'),
      ];
    }

    if (!createExecution) {
      return [null, new Error('Failed to create execution record')];
    }

    // Return the prepared data
    const result: PreparedData = {
      taskflow: {
        schema: taskflow.taskflowSchema,
        nodes: taskflow.taskflowSchema.nodes,
      },
      taskflowExecution: {
        id: createExecution.id,
        context: {},
        triggerData,
      },
      userProfile: { id: userId },
    };
    return [result, null];
  } catch (error) {
    return [null, error instanceof Error ? error : new Error(String(error))];
  }
}

export { createTaskflowExecution };
