import { Tournament } from '@n8n/tournament/dist/index.js';

/**
 * Resolves parameters using the tournament e.g.:
 * { "helloWorld": "{{trigger.hello}}" }       => { "helloWorld": "world" }
 * { "someValue": "={{node1.someValue}}"       => { "someValue": value } (literal value, not string)
 */
function evaluateNodeParameters(
  parameters: Record<string, any>,
  input: Record<string, any>,
  tournament: Tournament
): Record<string, any> {
  if (Array.isArray(parameters)) {
    return parameters.map(item => handleValue(item, input, tournament));
  }

  const resolved: Record<string, any> = {};
  for (const [key, value] of Object.entries(parameters)) {
    resolved[key] = handleValue(value, input, tournament);
  }
  return resolved;
}

/**
 * Handles a single value in the parameters object
 * Recursively processes nested objects and arrays
 */
function handleValue(value: any, input: Record<string, any>, tournament: Tournament) {
  if (typeof value === 'string') {
    // Check if the string starts with "=" followed by an expression in double curly braces
    // This indicates we want the literal value, not a string representation
    if (value.startsWith('=') && value.includes('{{') && value.includes('}}')) {
      // Remove the "=" prefix and evaluate the expression
      const template = value.substring(1);
      return tournament.execute(template, input);
    } else {
      // Regular template evaluation
      return tournament.execute(value, input);
    }
  } else if (typeof value === 'object' && value !== null) {
    return evaluateNodeParameters(value, input, tournament);
  } else {
    return value;
  }
}

export { evaluateNodeParameters, handleValue };
