import jsonLogic from 'json-logic-js';
import { isValid, parseISO } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';

function getJsonLogicFunctions(timeZone: string = 'UTC') {
  return {
    isoDateUser: (dateString?: string) => {
      const upper = dateString ? dateString.toUpperCase() : undefined;
      let date = upper ? parseISO(upper) : new Date();
      if (!isValid(date)) {
        date = new Date();
      }
      return formatInTimeZone(
        new Date(date),
        timeZone,
        "yyyy-MM-dd'T'HH:mm:ssXXX"
      );
    },
    JSON: {
      parse: (jsonString: string) => {
        try {
          return JSON.parse(jsonString);
        } catch {
          return null;
        }
      },
      stringify: (jsonObject: object) => {
        try {
          return JSON.stringify(jsonObject);
        } catch {
          return null;
        }
      },
    },
  };
}

function resolvePath(obj: any, path: string): any {
  return path.split('.').reduce((acc, part) => (acc ? acc[part] : undefined), obj);
}

function matchCondition(
  record: Record<string, any>,
  condition: any,
  timeZone: string = 'UTC'
): boolean {
  if (!condition || !Object.keys(condition).length) {
    return true;
  }

  const functions = getJsonLogicFunctions(timeZone);

  const toLowerKeys = (obj: any): any => {
    if (Array.isArray(obj)) return obj.map(toLowerKeys);
    if (obj && typeof obj === 'object') {
      return Object.fromEntries(
        Object.entries(obj).map(([k, v]) => [k.toLowerCase(), toLowerKeys(v)])
      );
    }
    return obj;
  };

  const lcFunctions = toLowerKeys(functions);
  const fnOperation = (spec: any, ...extra: any[]) => {
    let name: string;
    let args: any[] = [];

    if (Array.isArray(spec)) {
      [name, ...args] = spec;
    } else {
      name = spec;
    }

    args = args.concat(extra);
    const fn = resolvePath(lcFunctions, name.toLowerCase());
    if (typeof fn === 'function') {
      return fn(...args);
    }
    return null;
  };

  jsonLogic.add_operation('fn', fnOperation);
  try {
    const lowerCaseRecord = JSON.parse(JSON.stringify(record).toLowerCase());
    const lowerCaseCondition = JSON.parse(
      JSON.stringify(condition).toLowerCase()
    );
    return !!jsonLogic.apply(lowerCaseCondition, lowerCaseRecord);
  } finally {
    jsonLogic.rm_operation('fn');
  }
}

export { getJsonLogicFunctions, matchCondition };
