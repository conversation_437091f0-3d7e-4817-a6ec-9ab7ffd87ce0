import { strictEqual, deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { executeActionNode, ActionNodeDependencies } from './action-node';
import { NodeExecutionSubtypeParams } from '../types';
import { DebugTracer } from '../debugTracer';

// Mock Supabase client
const mockSupabase = {
  getConnection: () => Promise.resolve({ data: { id: 'connection-123' }, error: null }),
};

// Utilities to track calls
let actionCalls: any[] = [];
const mockRunner = (nango: any, params: any) => {
  actionCalls.push([nango, params]);
  return Promise.resolve({ success: true, data: { id: '123', name: 'Test Result' } });
};
const mockNango = {};
const deps: ActionNodeDependencies = {
  getPseudoNangoAction: () => mockNango as any,
  getRunner: () => mockRunner as any,
};

test('Action Node - executes provider action successfully', async () => {
  actionCalls = [];
  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'provider.test-provider.test-action',
      subtype: 'test-provider.test-action',
      parameters: { param1: 'value1', param2: 'value2' },
    },
    supabaseFacade: mockSupabase as any,
    executionId: 'exec-123',
    userId: 'user-123',
    userProfile: {},
    tracer: new DebugTracer(mockSupabase as any),
  };

  const result = await executeActionNode(params, deps);
  strictEqual(result.status, 'SUCCESS');
  deepStrictEqual(result.result, { success: true, data: { id: '123', name: 'Test Result' } });
  strictEqual(actionCalls.length, 1);
});

test('Action Node - pauses for confirmation when required', async () => {
  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'provider.test-provider.test-action',
      subtype: 'test-provider.test-action',
      parameters: { requiresConfirmation: true, param1: 'value1', param2: 'value2' },
    },
    supabaseFacade: mockSupabase as any,
    executionId: 'exec-123',
    userId: 'user-123',
    userProfile: {},
    tracer: new DebugTracer(mockSupabase as any),
  };

  const result = await executeActionNode(params, deps);
  strictEqual(result.status, 'PAUSED');
  strictEqual(result.hitlParams?.type, 'confirmation');
  deepStrictEqual(result.hitlParams?.parameters, { param1: 'value1', param2: 'value2' });
});

test('Action Node - proceeds with confirmation when resumePayload has confirmed', async () => {
  actionCalls = [];
  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'provider.test-provider.test-action',
      subtype: 'test-provider.test-action',
      parameters: { requiresConfirmation: true, param1: 'value1', param2: 'value2' },
    },
    supabaseFacade: mockSupabase as any,
    executionId: 'exec-123',
    userId: 'user-123',
    resumePayload: { confirmed: true },
    userProfile: {},
    tracer: new DebugTracer(mockSupabase as any),
  };

  const result = await executeActionNode(params, deps);
  strictEqual(result.status, 'SUCCESS');
  deepStrictEqual(result.result, { success: true, data: { id: '123', name: 'Test Result' } });
  strictEqual(actionCalls.length, 1);
});

test('Action Node - returns error when provider action fails', async () => {
  const failingDeps: ActionNodeDependencies = {
    getPseudoNangoAction: () => mockNango as any,
    getRunner: () => ((nango: any, params: any) => {
      return Promise.resolve({ error: 'bad request' });
    }) as any,
  };

  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'provider.test-provider.test-action',
      subtype: 'test-provider.test-action',
      parameters: { param1: 'value1' },
    },
    supabaseFacade: mockSupabase as any,
    executionId: 'exec-123',
    userId: 'user-123',
    userProfile: {},
    tracer: new DebugTracer(mockSupabase as any),
  };

  const result = await executeActionNode(params, failingDeps);
  strictEqual(result.status, 'ERROR');
  strictEqual(result.error, 'bad request');
});

test('Action Node - preserves HITL params on error with confirmation', async () => {
  const failingDeps: ActionNodeDependencies = {
    getPseudoNangoAction: () => mockNango as any,
    getRunner: () => ((nango: any, params: any) => {
      return Promise.resolve({ error: 'bad request' });
    }) as any,
  };

  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'provider.test-provider.test-action',
      subtype: 'test-provider.test-action',
      parameters: { param1: 'value1', requiresConfirmation: true },
    },
    supabaseFacade: mockSupabase as any,
    executionId: 'exec-123',
    userId: 'user-123',
    userProfile: {},
    resumePayload: { confirmed: true },
    tracer: new DebugTracer(mockSupabase as any),
  };

  const result = await executeActionNode(params, failingDeps);
  strictEqual(result.status, 'ERROR');
  strictEqual(result.error, 'bad request');
  strictEqual(result.hitlParams?.type, 'confirmation');
  deepStrictEqual(result.hitlParams?.parameters, { param1: 'value1' });
});
