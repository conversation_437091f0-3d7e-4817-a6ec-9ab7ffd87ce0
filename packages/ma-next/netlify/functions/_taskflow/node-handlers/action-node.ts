import { NodeExecutionSubtypeParams, NodeOutput } from '../types';
import { getRunner as defaultGetRunner } from '../../_tools/actions';
import { getPseudoNangoAction as defaultGetPseudoNangoAction } from '../../_nango/getPseudoNangoAction';

/**
 * Executes an action node that interacts with external providers via Nango
 *
 * @param params The node execution parameters
 * @returns The node execution output
 */
interface ActionNodeDependencies {
  getRunner?: typeof defaultGetRunner;
  getPseudoNangoAction?: typeof defaultGetPseudoNangoAction;
}

async function executeActionNode(
  {
    node: { subtype, parameters: { requiresConfirmation, ...parameters } = {} },
    supabaseFacade,
    resumePayload,
    userId,
    tracer,
  }: NodeExecutionSubtypeParams,
  deps: ActionNodeDependencies = {}
): Promise<NodeOutput> {
  tracer.addStep('[executeActionNode] start', { subtype, parameters });
  const { getRunner = defaultGetRunner, getPseudoNangoAction = defaultGetPseudoNangoAction } = deps;
  const [provider, action] = subtype.split('.', 2);

  // Find the user's connection for this provider
  const { data: connection, error: connError } = await supabaseFacade.getConnection(
    provider,
    userId
  );

  if (connError || !connection) {
    throw new Error(`No connection found for provider: ${provider}`);
  }
  tracer.addStep('[executeActionNode] connection found', { connectionId: connection.id });

  // Precompute HITL parameters for confirmation flows
  const hitlParams = requiresConfirmation
    ? {
        type: 'confirmation' as const,
        parameters,
        confirmationConfig: typeof requiresConfirmation === 'object' ? requiresConfirmation : {},
      }
    : undefined;

  // If confirmation is required and not provided, pause execution
  if (hitlParams && !resumePayload?.confirmed) {
    tracer.addStep('[executeActionNode] confirmation required, pausing execution');
    return {
      status: 'PAUSED',
      hitlParams,
    };
  }

  tracer.addStep('[executeActionNode] trigger', { provider, action, parameters });

  // Trigger the action via Nango
  try {
    const nango = getPseudoNangoAction(provider, connection.id);

    const runner = getRunner(provider, action);

    if (!runner) {
      return {
        status: 'ERROR',
        error: 'No handler found for: ${providerKey} - ${actionKey}',
      };
    }

    const result = await runner(nango, parameters);
    tracer.addStep('[executeActionNode] action result', { result });

    if (result && typeof result === 'object' && 'error' in result) {
      tracer.addStep('[executeActionNode] action returned error', { error: result.error });
      return {
        status: 'ERROR',
        error: result.error,
        ...(hitlParams ? { hitlParams } : {}),
      };
    }

    if (hitlParams) {
      tracer.addStep('[executeActionNode] requires confirmation', { hitlParams });
      return {
        status: 'SUCCESS',
        result,
        hitlParams,
      };
    }

    tracer.addStep('[executeActionNode] returning', { status: 'SUCCESS', result });

    return { status: 'SUCCESS', result };
  } catch (error) {
    tracer.addStep('[executeActionNode] error', {
      error: error instanceof Error ? error.message : String(error),
    });
    const errorMessage = `Error triggering action ${action} for provider ${provider}: ${
      error instanceof Error ? error.message : String(error)
    }`;
    tracer.addStep('[executeActionNode] returning error', { errorMessage });
    return {
      status: 'ERROR',
      error: errorMessage,
      ...(hitlParams ? { hitlParams } : {}),
    };
  }
}

export type { ActionNodeDependencies };
export { executeActionNode };
