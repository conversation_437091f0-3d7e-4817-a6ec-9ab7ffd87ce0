import { SupabaseClient } from '@supabase/supabase-js';

export class TaskflowSupabaseFacade {
  private client: SupabaseClient;

  constructor(client: SupabaseClient) {
    this.client = client;
  }

  getProfile(userId: string) {
    return this.client
      .from('profiles')
      .select('id, firstName, lastName, preferences')
      .eq('id', userId)
      .single();
  }

  getActiveExecution(executionId: string) {
    return this.client
      .from('taskflow_executions')
      .select('id, context, triggerData, taskflowId')
      .eq('id', executionId)
      .is('completedAt', null)
      .maybeSingle();
  }

  getUserTaskflow(taskflowId: string, userId: string) {
    return this.client
      .from('taskflows')
      .select(
        `
        taskflowSchema:schema,
        conversations!taskflows_conversationId_fkey!inner(
          id,
          userId
        )
      `
      )
      .eq('id', taskflowId)
      .eq('conversations.userId', userId)
      .single();
  }

  createExecution(taskflowId: string, triggerData?: Record<string, any>, isTest?: boolean) {
    return this.client
      .from('taskflow_executions')
      .insert({
        taskflowId,
        triggerData,
        startedAt: new Date(),
        context: {},
        status: 'RUNNING',
        isTest: isTest || false,
      })
      .select('id')
      .single();
  }

  updateExecution(executionId: string, updates: Record<string, any>) {
    return this.client.from('taskflow_executions').update(updates).eq('id', executionId);
  }

  getConnection(providerKey: string, userId: string) {
    return this.client
      .from('connections')
      .select('id')
      .eq('providerKey', providerKey)
      .eq('userId', userId)
      .maybeSingle();
  }

  getExecutionContext(executionId: string) {
    return this.client.from('taskflow_executions').select('context').eq('id', executionId).single();
  }

  createTaskflow(schema: any, conversationId: string, active: boolean) {
    return this.client
      .from('taskflows')
      .insert({ schema, conversationId, active })
      .select('id, schema, active')
      .single();
  }

  createSyncTrigger(params: {
    taskflowId: string;
    providerKey: string;
    model: string;
    syncKey: string;
    condition: any;
    syncScope: any;
  }) {
    return this.client.from('sync_triggers').insert(params);
  }

  createExecutionTrace(executionId: string, trace: any[]) {
    return this.client
      .from('execution_traces')
      .insert({ executionId, trace });
  }
}
