import { deepStrictEqual as assertEquals } from 'node:assert';
import { test } from 'node:test';
import { getJsonLogicFunctions, matchCondition } from '../jsonLogicUtils';

const { isoDateUser } = getJsonLogicFunctions('Pacific/Auckland');

test('isoDateUser converts to timezone', () => {
  const result = isoDateUser('2025-05-14T01:28:00Z');
  assertEquals(result, '2025-05-14T13:28:00+12:00');
});

test('matchCondition supports custom fn', () => {
  const record = { start: { dateTime: '2025-05-14T01:28:00Z' } };
  const condition = {
    '>': [
      { var: 'start.dateTime' },
      { fn: ['isoDateUser', '2025-05-13T01:28:00Z'] },
    ],
  };
  const result = matchCondition(record, condition, 'UTC');
  assertEquals(result, true);
});
