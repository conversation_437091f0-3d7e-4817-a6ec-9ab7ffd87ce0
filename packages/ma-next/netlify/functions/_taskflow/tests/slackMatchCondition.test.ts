import { deepStrictEqual as assertEquals } from 'node:assert';
import { test } from 'node:test';
import { matchCondition } from '../jsonLogicUtils';

const condition = {
  and: [
    { '==': [ { var: 'channel_id' }, 'C08TJRTK3FE' ] },
    { '!=': [ { var: 'message.files' }, [] ] },
  ],
};

const recordWithFile = {
  channel_id: 'C08TJRTK3FE',
  message: { files: [{ id: 'F001' }] },
};

const recordWithoutFile = {
  channel_id: 'C08TJRTK3FE',
  message: {},
};

const recordWrongChannel = {
  channel_id: 'DIFFERENT',
  message: { files: [{ id: 'F002' }] },
};

test('matches when channel and files condition satisfied', () => {
  assertEquals(matchCondition(recordWithFile, condition), true);
});

test('missing files still passes with current condition', () => {
  assertEquals(matchCondition(recordWithoutFile, condition), true);
});

test('different channel fails condition', () => {
  assertEquals(matchCondition(recordWrongChannel, condition), false);
});
