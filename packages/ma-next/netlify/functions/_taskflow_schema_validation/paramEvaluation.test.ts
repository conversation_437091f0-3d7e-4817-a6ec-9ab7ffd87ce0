import { test } from 'node:test';
import { strictEqual } from 'node:assert';
import { evaluateTemplate, validateParameterTemplates } from './paramEvaluation';
import type { JsonSchema } from './nodeSchemas';

const schema: JsonSchema = {
  type: 'object',
  properties: {
    trigger: {
      type: 'object',
      properties: {
        attendees: { type: 'array', items: { type: 'string' } },
      },
    },
  },
};

test('evaluateTemplate respects type transformations', () => {
  const template = '{{ trigger.attendees.join(",") }}';
  validateParameterTemplates(template, schema, ['trigger']);
  const result = evaluateTemplate(template, schema);
  strictEqual(typeof result, 'string');
});

// Additional test schemas derived from sample workflows
const senderSchema: JsonSchema = {
  type: 'object',
  properties: {
    trigger: {
      type: 'object',
      properties: {
        sender: { type: 'string' },
      },
    },
  },
};

const fileListSchema: JsonSchema = {
  type: 'object',
  properties: {
    node1: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              filename: { type: 'string' },
              status: { type: 'string' },
              additions: { type: 'number' },
              deletions: { type: 'number' },
              patch: { type: 'string' },
            },
          },
        },
      },
    },
  },
};

const stringifySchema: JsonSchema = {
  type: 'object',
  properties: {
    trigger: { type: 'object', properties: { any: { type: 'string' } } },
  },
};

const emptySchema: JsonSchema = { type: 'object', properties: {} };

// Expression using optional chaining and fallback
test('evaluateTemplate handles regex match with fallback', () => {
  const template = '{{ trigger.sender.match(/[^<]+<([^>]+)>/)?.[1] || trigger.sender }}';
  validateParameterTemplates(template, senderSchema, ['trigger']);
  const result = evaluateTemplate(template, senderSchema);
  strictEqual(typeof result, 'string');
});

// Expression building a string from mapped array
test('evaluateTemplate handles complex map join', () => {
  const template =
    "{{node1.files.map(file => `File: ${file.filename}\\nStatus: ${file.status}\\nAdditions: ${file.additions}\\nDeletions: ${file.deletions}\\n${file.patch ? 'Patch: ' + file.patch : ''}\\n\\n`).join('')}}";
  validateParameterTemplates(template, fileListSchema, ['node1']);
  const result = evaluateTemplate(template, fileListSchema);
  strictEqual(typeof result, 'string');
});

// Expression calling JSON.stringify
test('evaluateTemplate handles JSON.stringify call', () => {
  const template = '{{ JSON.stringify(trigger) }}';
  validateParameterTemplates(template, stringifySchema, ['trigger']);
  const result = evaluateTemplate(template, stringifySchema);
  // Tournament does not expose the JSON object, resulting in undefined
  strictEqual(result, undefined);
});

// Expression referencing unknown function results in undefined
test('evaluateTemplate returns undefined on unknown function', () => {
  const template = '{{ isoDateUser() }}';
  const result = evaluateTemplate(template, emptySchema);
  strictEqual(result, undefined);
});
