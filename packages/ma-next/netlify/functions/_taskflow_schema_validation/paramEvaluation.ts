import { JsonSchema } from './nodeSchemas';
import { Tournament } from '@n8n/tournament/dist/index.js';

function buildDummyFromSchema(schema: JsonSchema): any {
  switch (schema.type) {
    case 'object': {
      const obj: Record<string, any> = {};
      if (schema.properties) {
        for (const [k, v] of Object.entries(schema.properties)) {
          obj[k] = buildDummyFromSchema(v);
        }
      }
      return obj;
    }
    case 'array':
      return [buildDummyFromSchema(schema.items || { type: 'any' as any })];
    case 'string':
      return '';
    case 'number':
      return 0;
    case 'boolean':
      return false;
    default:
      return undefined;
  }
}

export function evaluateTemplate(template: string, schema: JsonSchema): unknown {
  const tournament = new Tournament();
  const context = buildDummyFromSchema(schema);
  try {
    return tournament.execute(template, context);
  } catch {
    return undefined;
  }
}

function schemaHasPath(schema: JsonSchema, path: string[]): boolean {
  let current: JsonSchema | undefined = schema;
  for (const key of path) {
    if (!current || current.type !== 'object' || !current.properties) return false;
    current = current.properties[key];
  }
  return !!current;
}

function extractPaths(expr: string, keys: string[], schema: JsonSchema): string[][] {
  const paths: string[][] = [];
  for (const key of keys) {
    const regex = new RegExp(`${key}(?:\\.[a-zA-Z0-9_]+)*`, 'g');
    for (const match of expr.matchAll(regex)) {
      const segments = match[0].split('.');
      const originalLength = segments.length;
      let current = [...segments];
      while (current.length && !schemaHasPath(schema, current)) current.pop();
      if (current.length === 0 || (current.length <= 1 && current.length < originalLength)) {
        throw new Error(`Invalid reference ${segments.join('.')} in template`);
      }
      paths.push(current);
    }
  }
  return paths;
}

function validateTemplateString(template: string, schema: JsonSchema, keys: string[]) {
  const placeholder = /{{\s*([^}]+)\s*}}/g;
  for (const [, expr] of template.matchAll(placeholder)) {
    const paths = extractPaths(expr, keys, schema);
    for (const path of paths) {
      if (!schemaHasPath(schema, path)) {
        throw new Error(`Invalid reference ${path.join('.')} in template`);
      }
    }
  }
}

export function validateParameterTemplates(params: any, schema: JsonSchema, keys: string[]) {
  if (typeof params === 'string') {
    validateTemplateString(params, schema, keys);
  } else if (Array.isArray(params)) {
    for (const item of params) validateParameterTemplates(item, schema, keys);
  } else if (typeof params === 'object' && params) {
    for (const value of Object.values(params)) validateParameterTemplates(value, schema, keys);
  }
}
