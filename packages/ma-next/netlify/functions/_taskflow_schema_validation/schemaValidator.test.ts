import { test } from 'node:test';
import { strictEqual, throws } from 'node:assert';
import { EMAIL_WORKFLOW_SCHEMA } from '../_agents/sampleWorkflows';
import { validateTaskflowSchema } from './schemaValidator';

// Successful validation of sample workflow
test('validate email workflow schema', () => {
  const result = validateTaskflowSchema(EMAIL_WORKFLOW_SCHEMA);
  strictEqual(result, true);
});

// Failure when referencing unknown trigger property
test('fails on bad template reference', () => {
  const bad = JSON.parse(JSON.stringify(EMAIL_WORKFLOW_SCHEMA));
  bad.nodes[1].parameters.prompt = '{{trigger.missing}}';
  throws(() => validateTaskflowSchema(bad));
});

