import type { DropboxFile, DropboxUploadFileInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: DropboxUploadFileInput
): Promise<DropboxFile | NangoError> {
  try {
    const { path, content, encoding = 'utf8', mode = 'add', autorename = false, mute = false } = input;

    if (!path) {
      return { error: { status: 400, message: 'Input validation failed: Path is required' } };
    }

    if (!content) {
      return { error: { status: 400, message: 'Input validation failed: Content is required' } };
    }

    const fileBuffer = Buffer.from(content, encoding as BufferEncoding);

    const uploadResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/upload',
      baseUrlOverride: 'https://content.dropboxapi.com',
      headers: {
        'Content-Type': 'application/octet-stream',
        'Dropbox-API-Arg': JSON.stringify({
          path,
          mode,
          autorename,
          mute,
          strict_conflict: false,
        }),
      },
      data: fileBuffer,
      retries: 3,
    });

    const sharedLinkResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/2/sharing/list_shared_links',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        path,
        direct_only: true,
      },
      retries: 3,
    });

    let linkData;
    if (sharedLinkResponse.data.links && sharedLinkResponse.data.links.length > 0) {
      linkData = sharedLinkResponse.data.links[0];
    } else {
      const createLinkResponse = await nango.proxy({
        method: 'POST',
        endpoint: '/2/sharing/create_shared_link_with_settings',
        headers: {
          'Content-Type': 'application/json',
        },
        data: { path },
        retries: 3,
      });
      linkData = createLinkResponse.data;
    }

    let downloadUrl = linkData.url;
    downloadUrl = downloadUrl.replace('www.dropbox.com', 'dl.dropboxusercontent.com');

    return {
      id: uploadResponse.data.id,
      name: uploadResponse.data.name,
      path_display: uploadResponse.data.path_display,
      path_lower: uploadResponse.data.path_lower,
      size: uploadResponse.data.size,
      content_hash: uploadResponse.data.content_hash,
      server_modified: uploadResponse.data.server_modified,
      download_url: downloadUrl,
    };
  } catch (error: any) {
    console.error('Error uploading file to Dropbox:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error_summary ||
      error?.message ||
      'An unknown error occurred while uploading file to Dropbox.';
    return { error: { status, message } };
  }
}
