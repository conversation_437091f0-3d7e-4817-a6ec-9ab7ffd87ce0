import type { GoogleCalendarEventDeleteInput, GoogleCalendarEventDeleteOutput, NangoAction } from '../models';

type NangoError = { error: { status: number; message: string } };

export default async function deleteEvent(
  nango: NangoAction,
  input: GoogleCalendarEventDeleteInput
): Promise<GoogleCalendarEventDeleteOutput | NangoError> {
  const { calendarId, eventId, sendUpdates } = input;

  const eventEndpoint = `/calendar/v3/calendars/${encodeURIComponent(calendarId)}/events/${encodeURIComponent(
    eventId
  )}`;

  const deleteEndpoint = `/calendar/v3/calendars/${encodeURIComponent(calendarId)}/events/${encodeURIComponent(
    eventId
  )}`;

  const queryParams: Record<string, string> = {};
  if (sendUpdates !== undefined) {
    queryParams['sendUpdates'] = sendUpdates;
  }

  try {
    // First, fetch the event to get its details before deletion
    const eventResponse = await nango.proxy({
      method: 'GET',
      endpoint: eventEndpoint,
      providerConfigKey: 'google-calendar',
      connectionId: nango.connectionId,
      retries: 3,
    });

    // Now delete the event
    await nango.proxy({
      method: 'DELETE',
      endpoint: deleteEndpoint,
      providerConfigKey: 'google-calendar',
      connectionId: nango.connectionId,
      params: queryParams,
      retries: 3,
    });

    // Return the event data with deletion timestamp
    return {
      event: eventResponse.data,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    const status = error.response?.status ?? 500;
    const message =
      error.response?.data?.error?.message ||
      error.message ||
      'Failed to delete Google Calendar event';

    console.error('Error deleting event:', error.response?.data || error);

    return { error: { status: status, message: message } };
  }
}
