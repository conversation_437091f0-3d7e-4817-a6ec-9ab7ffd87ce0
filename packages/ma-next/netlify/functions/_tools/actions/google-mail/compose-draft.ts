import type { GmailDraftInput, GmailDraftOutput, NangoAction } from '../models';
import { buildAttachmentLines } from './utils/attachmentHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailDraftInput
): Promise<GmailDraftOutput | NangoError> {
  const { recipient, subject, body, attachments } = input;

  try {
    if (!recipient || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(recipient)) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Invalid or missing recipient email address',
        },
      };
    }

    let base64EncodedEmail: string;

    if (attachments && attachments.length > 0) {
      const boundary = '----=_Part_' + Math.random().toString(36).slice(2);
      const lines: string[] = [
        `To: ${recipient}`,
        `Subject: ${subject}`,
        'MIME-Version: 1.0',
        `Content-Type: multipart/mixed; boundary="${boundary}"`,
        '',
        `--${boundary}`,
        'Content-Type: text/plain; charset="UTF-8"',
        '',
        body || '',
      ];

      const attachmentLines = await buildAttachmentLines(nango, attachments, boundary);
      lines.push(...attachmentLines);
      lines.push(`--${boundary}--`);
      const email = lines.join('\n');

      base64EncodedEmail = Buffer.from(email)
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
    } else {
      const email = [
        `To: ${recipient}`,
        `Subject: ${subject}`,
        'MIME-Version: 1.0',
        'Content-Type: text/plain; charset=UTF-8',
        '',
        body || '',
      ].join('\n');

      base64EncodedEmail = Buffer.from(email)
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
    }

    const draftResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/gmail/v1/users/me/drafts',
      data: {
        message: {
          raw: base64EncodedEmail,
        },
      },
      retries: 10,
    });

    return {
      id: draftResponse.data.id,
      threadId: draftResponse.data.message?.threadId || null,
    };
  } catch (error: any) {
    console.error('Error composing draft:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while composing the draft.';
    return { error: { status, message } };
  }
}
