import type { NangoAction, UrlAccessibleFile } from '../../models';

export type ErrorResponse = { error: { status: number; message: string } };

export async function getSlackUserToken(
  nango: NangoAction,
  connectionId: string
): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection('slack', connectionId);
    if (!('raw' in connection.credentials)) {
      await nango.log('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = (connection.credentials as any)['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];
    if (!userToken || typeof userToken !== 'string') {
      await nango.log('User token not found or invalid in Slack connection credentials.');
      return { error: { status: 500, message: 'User token not found or invalid in Slack connection credentials.' } };
    }
    return userToken;
  } catch (err: any) {
    await nango.log(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}

export async function buildAttachmentLines(
  nango: NangoAction,
  attachments: UrlAccessibleFile[],
  boundary: string
): Promise<string[]> {
  const lines: string[] = [];

  for (const file of attachments) {
    try {
      let response: { data: ArrayBuffer; headers: Record<string, string> } | any;
      if (file.authentication) {
        if (file.authentication.providerKey === 'slack') {
          const tokenResult = await getSlackUserToken(nango, file.authentication.connectionId);
          if (typeof tokenResult !== 'string') {
            console.error('Error getting Slack user token:', (tokenResult as ErrorResponse).error.message);
            continue;
          }
          const res = await fetch(file.url, {
            headers: { Authorization: `Bearer ${tokenResult}` },
          });
          if (!res.ok) throw new Error(`Failed to fetch attachment: ${res.statusText}`);
          response = {
            data: await res.arrayBuffer(),
            headers: { 'content-type': res.headers.get('content-type') || 'application/octet-stream' },
          };
        } else {
          response = await nango.proxy({
            method: 'GET',
            endpoint: file.url,
            connectionId: file.authentication.connectionId,
            providerConfigKey: file.authentication.providerKey,
            responseType: 'arraybuffer',
            baseUrlOverride: '',
          });
        }
      } else {
        const res = await fetch(file.url);
        if (!res.ok) throw new Error(`Failed to fetch attachment: ${res.statusText}`);
        response = {
          data: await res.arrayBuffer(),
          headers: { 'content-type': res.headers.get('content-type') || 'application/octet-stream' },
        };
      }

      const buffer = Buffer.from(response.data);
      const b64 = buffer.toString('base64');
      const filename = file.url.split('/').pop() || 'attachment';
      const mime = response.headers['content-type'] || 'application/octet-stream';

      lines.push(`--${boundary}`);
      lines.push(`Content-Type: ${mime}`);
      lines.push(`Content-Disposition: attachment; filename="${filename}"`);
      lines.push('Content-Transfer-Encoding: base64');
      lines.push('');
      lines.push(b64);
    } catch (err) {
      console.error('Error fetching attachment:', err);
    }
  }

  return lines;
}
