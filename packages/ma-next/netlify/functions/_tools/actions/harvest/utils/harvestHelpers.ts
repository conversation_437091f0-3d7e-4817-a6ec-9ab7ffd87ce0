import type { NangoAction } from '../../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

import type { HarvestCompany } from '../../models';

const METADATA_KEY = 'harvestCompanySettings';

/**
 * Helper function to get the Harvest account ID for the authenticated user
 * This uses the id.getharvest.com/api/v2/accounts endpoint which doesn't require the Harvest-Account-Id header
 */
export async function getHarvestAccountId(nango: NangoAction): Promise<string | NangoError> {
  try {
    const metadata = await nango.getMetadata();

    if (metadata && metadata['account_id']) {
      return metadata['account_id'].toString();
    }

    const response = await nango.proxy({
      method: 'GET',
      endpoint: '/api/v2/accounts',
      baseUrlOverride: 'https://id.getharvest.com',
      headers: {
        'User-Agent': 'MakeAgent (<EMAIL>)',
      },
    });

    if (!response.data || !response.data.accounts || response.data.accounts.length === 0) {
      return { error: { status: 404, message: 'No Harvest accounts found for this user' } };
    }

    const harvestAccount = response.data.accounts.find(
      (account: { id: number; product: string }) => account.product === 'harvest'
    );

    if (!harvestAccount) {
      return { error: { status: 404, message: 'No Harvest accounts found for this user' } };
    }

    const accountId = harvestAccount.id.toString();

    try {
      await nango.updateMetadata({
        account_id: accountId,
      });
    } catch (error: any) {
      console.warn('Could not update metadata, but will continue with the account ID:', error);
    }

    return accountId;
  } catch (error: any) {
    console.error('Error fetching Harvest account ID:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while fetching the Harvest account ID.';
    return { error: { status, message } };
  }
}

export async function getCompanySettings(nango: NangoAction): Promise<HarvestCompany | NangoError> {
  try {
    const cachedSettings = (await nango.getMetadata()) as { [METADATA_KEY]?: HarvestCompany };
    if (cachedSettings && cachedSettings[METADATA_KEY]) {
      return cachedSettings[METADATA_KEY];
    }
  } catch (error) {
    console.warn('Failed to retrieve metadata, fetching from API:', error);
  }

  const accountIdResult = await getHarvestAccountId(nango);
  if ((accountIdResult as any)?.error) {
    return accountIdResult as NangoError;
  }

  let harvestAccountId: string = '';
  if (typeof accountIdResult === 'string') {
    harvestAccountId = accountIdResult;
  } else if ((accountIdResult as any).error) {
    return accountIdResult;
  }

  const response = await nango.proxy({
    endpoint: '/v2/company',
    method: 'GET',
    headers: { 'Harvest-Account-Id': harvestAccountId },
    retries: 2,
  });

  if (response.status !== 200 || !response.data) {
    return {
      error: {
        status: response.status,
        message: `FETCH_COMPANY_FAILED: Failed to fetch company settings. Status: ${response.status}. Response: ${JSON.stringify(response.data)}`,
      },
    };
  }

  const companyData = response.data as HarvestCompany;

  try {
    await nango.updateMetadata({ [METADATA_KEY]: companyData });
  } catch (error: any) {
    console.error('Failed to set metadata:', error);
  }

  return companyData;
}
