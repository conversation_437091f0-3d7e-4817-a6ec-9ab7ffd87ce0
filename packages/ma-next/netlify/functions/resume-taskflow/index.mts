import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { debug } from '../_shared/debug';
import { resumeTaskFlow } from '../_taskflow/index';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';


interface ResumeTaskflowRequest {
  executionId: string;
  nodeId: string;
  data: Record<string, any>;
  force?: boolean;
}

export default async function handler(req: Request) {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) {
    return corsResponse;
  }

  try {
    // Authenticate the request
    const [authErrorResponse, user] = await initAuthenticate(req);

    if (authErrorResponse) {
      return authErrorResponse;
    }

    // Parse the request body
    const { executionId, nodeId, data, force } = (await req.json()) as ResumeTaskflowRequest;

    debug('Resume taskflow request:', { executionId, nodeId, data, force });

    if (!executionId || !nodeId) {
      debug('executionId and nodeId are required');
      return validationError('executionId and nodeId are required');
    }

    // Initialize service role supabase client for taskflow operations
    const serviceRoleSupabase = initServiceRoleSupabase();

    // Resume the taskflow execution
    const resumeData = { executionId, nodeId, data, force };

    debug('Resuming taskflow with data:', resumeData);
    debug('User ID:', user.id);

    const supabaseFacade = new TaskflowSupabaseFacade(serviceRoleSupabase);

    const [result, error] = await resumeTaskFlow({
      supabaseFacade,
      userId: user.id,
      resumeData,
    });

    if (error) {
      console.error('Error resuming taskflow:', error);
      return errorResponse(error);
    }

    debug('Taskflow resumed successfully:', result, JSON.stringify(result));

    // Return the result
    return new Response(JSON.stringify(result), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error in resume-taskflow:', error);
    return errorResponse(error);
  }
}
