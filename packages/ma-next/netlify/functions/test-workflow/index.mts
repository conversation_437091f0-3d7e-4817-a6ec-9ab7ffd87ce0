import { initNango } from '../_shared/nango';
import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { debug } from '../_shared/debug';
import { createExecution, triggerWithExecution } from '../_taskflow/index';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';
import {
  filterRecords,
  heuristicChronologicalSort,
} from '../_shared/syncUtils';
import type { Context } from '@netlify/functions';

const DEFAULT_RECORD_LIMIT = 100;

interface TestWorkflowRequest {
  taskflowId: string;
  modelIds?: string[];
}

const nango = initNango();

export default async function handler(req: Request, context: Context) {
  try {
    // Handle CORS
    debug('Handling CORS');
    const corsResponse = handleCors(req);
    if (corsResponse) {
      debug('CORS response:', corsResponse);
      return corsResponse;
    }

    // Validate request method
    debug('Validating request method');
    if (req.method !== 'POST') {
      return validationError('Method not allowed');
    }

    // Parse request body
    debug('Parsing request body');
    const { taskflowId, modelIds } = (await req.json()) as TestWorkflowRequest;

    debug('Parsed request body:', `taskflowId: ${taskflowId}`, `modelIds: ${modelIds}`);

    if (!taskflowId) {
      return validationError('Taskflow ID is required');
    }

    // Authenticate request and get Supabase clients
    debug('Authenticating request and getting Supabase clients');
    const authResult = await initAuthenticate(req);
    if (authResult.length === 1) {
      debug('Authentication error:', authResult[0]);
      return authResult[0];
    }

    const [, user, supabaseAdmin, supabaseUser] = authResult;

    // Get the taskflow
    debug('Getting taskflow');
    const { data: taskflow, error: taskflowError } = await supabaseUser
      .from('taskflows')
      .select('id, schema')
      .eq('id', taskflowId)
      .single();

    if (taskflowError || !taskflow) {
      return errorResponse(`Taskflow not found: ${taskflowError?.message}`, 404);
    }

    // Get the trigger node from the taskflow schema
    debug('Getting trigger node from schema');
    const triggerNode = taskflow.schema?.triggers?.[0];

    if (!triggerNode) {
      return errorResponse('No trigger node found in taskflow schema', 400);
    }

    // Extract provider information from the trigger node
    debug('Extracting provider information');
    const providerKey = triggerNode.parameters?.providerKey;
    const model = triggerNode.parameters?.model;
    const syncKey = triggerNode.parameters?.syncKey;

    if (!providerKey) {
      return errorResponse('Trigger node missing providerKey', 400);
    }

    // Get the connection for the provider
    debug('Getting connection for provider:', providerKey);
    const { data: connection, error: connectionError } = await supabaseUser
      .from('connections')
      .select('id')
      .eq('providerKey', providerKey)
      .single();

    if (connectionError || !connection) {
      return errorResponse(`No connection found for provider: ${connectionError?.message}`, 404);
    }

    // Get test data from Nango
    debug('Getting test data from Nango');
    let testRecord;
    try {
      const baseParams = {
        providerConfigKey: providerKey,
        connectionId: connection.id,
        model,
        limit: DEFAULT_RECORD_LIMIT,
        ...(modelIds && { ids: modelIds }),
      };

      const now = Date.now();
      const day = new Date(now - 24 * 60 * 60 * 1000).toISOString();
      const week = new Date(now - 7 * 24 * 60 * 60 * 1000).toISOString();

      const [dayRes, weekRes, allRes] = await Promise.allSettled([
        nango.listRecords({ ...baseParams, modifiedAfter: day }),
        nango.listRecords({ ...baseParams, modifiedAfter: week }),
        nango.listRecords(baseParams),
      ]);

      const unwrap = (r: PromiseSettledResult<any>) => {
        if (r.status === 'fulfilled') return r.value;
        debug('listRecords error:', r.reason);
        return { records: [], next_cursor: undefined };
      };

      const dayData = unwrap(dayRes);
      const weekData = unwrap(weekRes);
      const allData = unwrap(allRes);

      let allRecords: any[] = [];
      if (allData.records && !allData.next_cursor) {
        allRecords = allData.records;
      } else if (weekData.records && !weekData.next_cursor) {
        allRecords = weekData.records;
      } else if (dayData.records && !dayData.next_cursor) {
        allRecords = dayData.records;
      } else {
        allRecords = dayData.records ?? [];
        let cursor = dayData.next_cursor;
        while (cursor) {
          const next = await nango.listRecords({ ...baseParams, cursor });
          allRecords.push(...(next.records || []));
          cursor = next.next_cursor;
        }
      }

      if (!allRecords.length) {
        return errorResponse('No records found to test with', 404);
      }

      allRecords.reverse();
      const sortedRecords = heuristicChronologicalSort(allRecords);

      // Get sync trigger conditions for this taskflow
      debug('Getting sync trigger conditions');
      const { data: syncTriggers, error: syncTriggersError } = await supabaseUser
        .from('sync_triggers')
        .select('condition')
        .eq('taskflowId', taskflowId)
        .eq('providerKey', providerKey)
        .eq('model', model);

      if (syncTriggersError) {
        debug(`Error fetching sync triggers: ${JSON.stringify(syncTriggersError)}`);
        return errorResponse(
          `Failed to get sync trigger conditions: ${syncTriggersError.message}`,
          500
        );
      }

      // Find a record that matches the sync trigger condition (if any)
      if (syncTriggers && syncTriggers.length > 0) {
        for (const syncTrigger of syncTriggers) {
          const records = filterRecords(sortedRecords, syncTrigger.condition);
          if (records.length) {
            testRecord = records[0];
            break;
          }
        }
        if (!testRecord) {
          return errorResponse('No records found matching the sync trigger condition', 404);
        }
      } else {
        // If no sync trigger condition, just use the first record
        testRecord = sortedRecords[0];
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return errorResponse(`Failed to get test data: ${errorMessage}`, 500);
    }

    // Create a test execution
    debug('Creating test execution');

    // Create the execution using the taskflow function
    const supabaseFacade = new TaskflowSupabaseFacade(supabaseAdmin);

    const [preparedData, error] = await createExecution({
      taskflowId: taskflow.id,
      userId: user.id,
      triggerData: testRecord,
      supabaseFacade,
      isTest: true, // Mark this as a test execution
    });

    if (error) {
      return errorResponse(`Failed to create execution: ${error.message}`, 500);
    }

    const taskflowExecutionId = preparedData?.taskflowExecution?.id;

    if (!taskflowExecutionId) {
      return errorResponse('Failed to create execution: No execution data returned', 500);
    }

    // Prepare response and schedule background tasks
    const response = new Response(JSON.stringify({ executionId: taskflowExecutionId }), {
      headers: corsHeaders,
      status: 200,
    });
    // Perform update and trigger in background
    context.waitUntil(
      (async () => {
        debug('Updating taskflow with test execution ID');
        const { error: updateError } = await supabaseAdmin
          .from('taskflows')
          .update({ testExecutionId: taskflowExecutionId })
          .eq('id', taskflow.id);
        if (updateError) debug('Error updating taskflow with test execution ID:', updateError);
        debug('Triggering execution as side effect');
        try {
          await triggerWithExecution({
            taskflowExecutionId,
            userId: user.id,
            supabaseFacade,
          });
        } catch (error: any) {
          debug('Error triggering execution:', error);
        }
      })()
    );
    return response;
  } catch (error) {
    debug('Error in handler:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return errorResponse(`Internal error: ${errorMessage}`, 500);
  }
}
