import { useState } from 'react';
import { Modal } from './Modal';
import { useModal } from 'hooks/useModal';
import { useAgentExecutions } from 'hooks/useAgentExecutions';
import { RichResultDisplay } from '../rich-ui/RichResultDisplay';
import { RichSyncOutputs } from '../rich-ui/RichSyncOutputs';
import { Clock, Play, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import clsx from 'clsx';
import { useChatContext } from 'providers/ChatContext';
import { useCurrentAgent } from 'hooks/useCurrentAgent';
import { useTaskflow } from 'hooks/useTaskflow';

function AgentExecutionsModal() {
  const { isOpen } = useModal();
  const chat = useChatContext();
  const { taskflow } = useTaskflow(chat.currentTaskflowId);

  // Get taskflow info from chat context
  const taskflowId = chat.currentTaskflowId;
  const displayNodes = taskflow?.schema?.nodes || [];

  const { executions, loading, error } = useAgentExecutions(taskflowId || '');

  const formatDuration = (startedAt: string, completedAt?: string) => {
    const start = new Date(startedAt);
    const end = completedAt ? new Date(completedAt) : new Date();
    const durationMs = end.getTime() - start.getTime();

    if (durationMs < 1000) {
      return `${durationMs}ms`;
    } else if (durationMs < 60000) {
      return `${Math.round(durationMs / 1000)}s`;
    } else {
      return `${Math.round(durationMs / 60000)}m`;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    if (diffHours < 1) {
      const diffMinutes = Math.round(diffMs / (1000 * 60));
      return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${Math.round(diffHours)}h ago`;
    } else if (diffDays < 7) {
      return `${Math.round(diffDays)}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getStatusIcon = (status?: string, completedAt?: string) => {
    if (!completedAt && status !== 'COMPLETED') {
      return <Play className="w-4 h-4 text-blue-500" />;
    }

    switch (status) {
      case 'COMPLETED':
      case 'SUCCESS':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'ERROR':
      case 'FAILED':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getExecutionData = (execution: any) => {
    if (!displayNodes.length) return { triggerData: null, output: null, actionParameters: null };

    const triggerNode = displayNodes[0];
    const actionNode = displayNodes[displayNodes.length - 1];

    // Get trigger data
    const triggerData = execution.triggerData;

    // Get action output and parameters from context
    const actionNodeId = actionNode?.id;
    const actionContext = actionNodeId ? execution.context[actionNodeId] : null
    const output = actionContext?.output;
    const actionParameters = actionContext?.parameters;

    return { triggerData, output, actionParameters };
  };

  // Don't render if no taskflow
  if (!taskflowId) {
    return null;
  }

  if (loading) {
    return (
      <Modal isOpen={isOpen('agentExecutions')} title="Agent Execution History" mediumSize>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </Modal>
    );
  }

  if (error) {
    return (
      <Modal isOpen={isOpen('agentExecutions')} title="Agent Execution History" mediumSize>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">{error}</p>
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen('agentExecutions')} title="Agent Execution History" mediumSize>
      <div className="max-h-[70vh] min-h-[50vh] overflow-y-auto">
        {executions.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">No executions yet</p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              Test your agent to see execution history here
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {executions.map(execution => {
              const { triggerData, output, actionParameters } = getExecutionData(execution);
              const triggerNode = taskflow?.schema.triggers?.[0];
              const actionNode = displayNodes[displayNodes.length - 1];

              return (
                <div
                  key={execution.id}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
                >
                  {/* Execution Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(execution.status, execution.completedAt)}
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {execution.isTest ? 'Test Run' : 'Live Run'}
                          </span>
                          {execution.isTest && (
                            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-200">
                              Test
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDate(execution.startedAt)} • Duration:{' '}
                          {formatDuration(execution.startedAt, execution.completedAt)}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Execution Data */}
                  <div className="flex flex-col sm:flex-row sm:space-x-4">
                    {triggerData && (
                      <div className="sm:w-1/2 mb-4 sm:mb-0">
                        <RichSyncOutputs
                          output={triggerData}
                          providerKey={triggerNode.parameters.providerKey}
                          syncKey={triggerNode.parameters.syncKey}
                        />
                      </div>
                    )}
                    {output && (
                      <div className="sm:w-1/2">
                        <RichResultDisplay
                          result={output}
                          providerKey={actionNode.type.split('.')[1]}
                          actionKey={actionNode.type.split('.')[2]}
                          actionParameters={actionParameters}
                          context={actionNode.type.split('.')[2]}
                        />
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </Modal>
  );
}

export { AgentExecutionsModal };
