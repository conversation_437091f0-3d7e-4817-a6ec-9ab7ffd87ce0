import { useRef, useState, useEffect } from 'react';
import { AgentNodes } from './AgentNodes';
import { AgentControlPanel } from './AgentControlPanel';
import { deriveTestStatus } from './deriveTestStatus';
import { useResetTestStatus } from './useResetTestStatus';
import { useTaskflowExecution } from 'hooks/useTaskflowExecution';
import { TestExecutionDisplay } from './TestExecutionDisplay';
import { TestStatus } from 'src/types';
import { supabase } from 'lib/supabase';
import { invokeFunction } from 'utils/invokeFunction';
import { useModal } from 'hooks/useModal';
import { History } from 'lucide-react';

interface TaskflowAgentPanelProps {
  taskflow: any;
  setTaskflow: React.Dispatch<React.SetStateAction<any>>;
  advancedOptions: boolean;
  setAdvancedOptions: React.Dispatch<React.SetStateAction<boolean>>;
  setupIsComplete: boolean;
  displayNodes: any[];
}

function TaskflowAgentPanel({
  taskflow,
  setTaskflow,
  advancedOptions,
  setAdvancedOptions,
  setupIsComplete,
  displayNodes,
}: TaskflowAgentPanelProps) {
  const agentPanelRef = useRef<HTMLDivElement>(null);
  const [pendingTest, setPendingTest] = useState<TestStatus | null>(null);
  const [execution, setExecution] = useState<any | null>(null);
  const { openModal } = useModal();

  const toggleActive = async () => {
    if (!taskflow) return;

    try {
      const { error } = await supabase
        .from('taskflows')
        .update({ active: !taskflow.active })
        .eq('id', taskflow.id);

      if (error) throw error;

      setTaskflow({
        ...taskflow,
        active: !taskflow.active,
      });
    } catch (err) {
      console.error('Error toggling taskflow active state:', err);
    }
  };

  if (!taskflow?.schema || (!taskflow.schema.nodes?.length && !taskflow.schema.triggers?.length)) {
    return null;
  }

  if (displayNodes.length === 0) {
    return null;
  }

  const handleTest = async () => {
    if (setupIsComplete) {
      setPendingTest('simulating');
      try {
        const { data, error } = await invokeFunction('test-workflow', {
          body: { taskflowId: taskflow.id },
        });
        if (error) {
          console.error('Failed to start test:', error);
          setPendingTest(null);
        } else if (data?.executionId) {
          setTaskflow({ ...taskflow, testExecutionId: data.executionId });
          setPendingTest(null);
        } else {
          setPendingTest(null);
        }
      } catch (error) {
        console.error('Failed to start test:', error);
        setPendingTest(null);
      }
    }
  };

  const testExecutionId = taskflow.testExecutionId;

  const handleHistoryClick = () => {
    openModal('agentExecutions');
  };

  return (
    <>
      <div className="flex items-center justify-between mt-8 mb-2">
        <h3 className="text-md font-bold text-gray-700 dark:text-gray-300">Agent</h3>
        <button
          onClick={handleHistoryClick}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          title="View execution history"
        >
          <History className="w-5 h-5" />
        </button>
      </div>

      {/* Agent Flow Visualization Panel */}
      <div ref={agentPanelRef} className="rounded-lg bg-white dark:bg-gray-800 p-5">
        {testExecutionId ? (
          <AgentNodesWithTestStatus
            testExecutionId={testExecutionId}
            taskflowId={taskflow.id}
            agentPanelRef={agentPanelRef}
            setExecution={setExecution}
            nodes={displayNodes}
            isActive={taskflow.active}
            advancedOptions={advancedOptions}
            setAdvancedOptions={setAdvancedOptions}
            setTaskflow={setTaskflow}
          />
        ) : (
          <AgentNodes
            nodes={displayNodes}
            isActive={taskflow.active}
            advancedOptions={advancedOptions}
            setAdvancedOptions={setAdvancedOptions}
            testStatus={pendingTest || 'idle'}
          />
        )}
      </div>

      {/* Test Button Panel */}
      <AgentControlPanel
        setupIsComplete={setupIsComplete}
        isTesting={!!testExecutionId || !!pendingTest}
        isActive={taskflow.active}
        onTest={handleTest}
        onToggleActive={toggleActive}
      >
        {testExecutionId && execution && execution.id === testExecutionId && (
          <TestExecutionDisplay nodes={displayNodes} execution={execution} />
        )}
      </AgentControlPanel>
    </>
  );
}

interface AgentNodesWithTestStatusProps {
  testExecutionId: string;
  taskflowId: string;
  agentPanelRef: React.RefObject<HTMLDivElement | null>;
  nodes: any[];
  isActive: boolean;
  advancedOptions: boolean;
  setAdvancedOptions: React.Dispatch<React.SetStateAction<boolean>>;
  setExecution: React.Dispatch<React.SetStateAction<any | null>>;
  [key: string]: any;
}

function AgentNodesWithTestStatus({
  testExecutionId,
  taskflowId,
  agentPanelRef,
  setTaskflow,
  setExecution,
  ...props
}: AgentNodesWithTestStatusProps) {
  const { execution, loading, error } = useTaskflowExecution(testExecutionId);
  const [waiting, setWaiting] = useState(true);

  // Reset waiting state whenever a new executionId is provided
  useEffect(() => {
    setWaiting(true);
  }, [testExecutionId]);

  useEffect(() => {
    if (execution && execution.status && execution.status !== 'PENDING') {
      setWaiting(false);
    }
  }, [execution]);

  useEffect(() => {
    setExecution(execution);
  }, [execution, setExecution]);

  const testStatus: TestStatus = waiting ? 'simulating' : deriveTestStatus(execution);

  useResetTestStatus(agentPanelRef, taskflowId, setTaskflow, testStatus);
  return (
    <>
      <AgentNodes {...props} testStatus={testStatus} />
    </>
  );
}

export { TaskflowAgentPanel };
