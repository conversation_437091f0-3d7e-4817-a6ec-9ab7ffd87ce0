import { GmailDraftDisplay } from './rich-parameters/GmailDraftDisplay';
import { GoogleCalendarEventDisplay } from './rich-parameters/GoogleCalendarEventDisplay';
import { TwitterPostDisplay } from './rich-parameters/TwitterPostDisplay';
import { SlackListChannelsDisplay } from './rich-parameters/SlackListChannelsDisplay';
import { SlackSendMessageDisplay } from './rich-parameters/SlackSendMessageDisplay';
import { SlackSearchMessagesDisplay } from './rich-parameters/SlackSearchMessagesDisplay';
import { XSocialPostDisplay } from './rich-parameters/XSocialPostDisplay';
import { NotionCreatePageDisplay } from './rich-parameters/NotionCreatePageDisplay';
import { NotionSearchDisplay } from './rich-parameters/NotionSearchDisplay';
import { GmailReplyDraftDisplay } from './rich-parameters/GmailReplyDraftDisplay';

const RICH_PARAM_MODEL_COMPONENTS: Record<string, React.FC<{ parameters: any }>> = {
  GmailDraftInput: GmailDraftDisplay,
  GmailSendEmailInput: GmailDraftDisplay,
  GmailReplyDraftInput: GmailReplyDraftDisplay,
  GoogleCalendarEventInput: GoogleCalendarEventDisplay,
  TwitterPostInput: TwitterPostDisplay,
  SlackListChannelsInput: SlackListChannelsDisplay,
  SlackSendMessageInput: SlackSendMessageDisplay,
  SlackSearchMessagesInput: SlackSearchMessagesDisplay,
  XSocialPostInput: XSocialPostDisplay,
  NotionCreatePageInput: NotionCreatePageDisplay,
  NotionSearchInput: NotionSearchDisplay,
};

interface RichParameterModelDisplayProps {
  modelName: string;
  parameters: any;
}

function RichParameterModelDisplay({ modelName, parameters }: RichParameterModelDisplayProps) {
  const DisplayComponent = RICH_PARAM_MODEL_COMPONENTS[modelName];
  if (!DisplayComponent) return null;
  return <DisplayComponent parameters={parameters} />;
}

RichParameterModelDisplay.canDisplay = (modelName: string) => {
  return Boolean(RICH_PARAM_MODEL_COMPONENTS[modelName]);
};

export { RichParameterModelDisplay };

