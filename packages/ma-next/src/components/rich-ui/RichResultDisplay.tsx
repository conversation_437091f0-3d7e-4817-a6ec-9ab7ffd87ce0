import { ACTION_OUTPUTS_KEYED } from 'src/config/nangoConstants';
import { RichResultModelDisplay } from './RichResultModelDisplay';

interface RichResultDisplayProps {
  result?: any;
  providerKey: string;
  actionKey: string;
  actionParameters?: Record<string, any>;
  context?: string;
}

function RichResultDisplay({
  result,
  providerKey,
  actionKey,
  actionParameters,
  context,
}: RichResultDisplayProps) {
  const modelName =
    ACTION_OUTPUTS_KEYED[`${providerKey}:${actionKey}` as keyof typeof ACTION_OUTPUTS_KEYED];

  if (!modelName || !RichResultModelDisplay.canDisplay(modelName)) return null;

  return (
    <RichResultModelDisplay
      modelName={modelName}
      result={result}
      actionParameters={actionParameters}
      context={context}
    />
  );
}

RichResultDisplay.canDisplay = (providerKey: string, actionKey: string): boolean => {
  const modelName =
    ACTION_OUTPUTS_KEYED[`${providerKey}:${actionKey}` as keyof typeof ACTION_OUTPUTS_KEYED];
  return Boolean(modelName && RichResultModelDisplay.canDisplay(modelName));
};

export { RichResultDisplay };
