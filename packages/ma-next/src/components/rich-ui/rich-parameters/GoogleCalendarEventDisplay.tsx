import { format, parseISO } from 'date-fns';
import { Calendar, Clock, MapPin, Users } from 'lucide-react';
import { GoogleCalendarEventInput } from 'src/config/nangoModels';

interface GoogleCalendarEventDisplayProps {
  parameters: GoogleCalendarEventInput;
}

/**
 * Renders a rich display of Google Calendar event parameters
 */
function GoogleCalendarEventDisplay({ parameters }: GoogleCalendarEventDisplayProps) {
  // Extract parameters
  const summary = parameters.summary || '';
  const description = parameters.description || '';
  const location = parameters.location || '';
  const start = parameters.start || '';
  const end = parameters.end || '';
  const timeZone = parameters.timeZone || '';
  const attendees = parameters.attendees || [];

  // Format dates
  let formattedStart = '';
  let formattedEnd = '';
  let isAllDay = false;

  // Check if this is an all-day event (date only) or a timed event
  if (start && !start?.includes?.('T') && !start?.includes?.(':')) {
    isAllDay = true;
    try {
      formattedStart = format(parseISO(start), 'MMM d, yyyy');
    } catch (e) {
      formattedStart = start;
    }
  } else if (start) {
    try {
      formattedStart = format(parseISO(start), 'MMM d, yyyy h:mm a');
    } catch (e) {
      formattedStart = start;
    }
  }

  if (end && !end?.includes?.('T') && !end?.includes?.(':')) {
    try {
      // For all-day events, the end date is exclusive, so subtract one day for display
      const endDate = new Date(end);
      endDate.setDate(endDate.getDate() - 1);
      formattedEnd = format(endDate, 'MMM d, yyyy');
    } catch (e) {
      formattedEnd = end;
    }
  } else if (end) {
    try {
      formattedEnd = format(parseISO(end), 'MMM d, yyyy h:mm a');
    } catch (e) {
      formattedEnd = end;
    }
  }

  // Determine if the event spans multiple days
  const isMultiDay = formattedStart !== formattedEnd && formattedEnd !== '';

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-800">
      {/* Event content */}
      <div className="p-5">
        {/* Event header with icon */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">Calendar Event</h3>
        </div>

        {/* Event title */}
        <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">{summary}</h4>

        <div className="space-y-4 mb-4">
          {/* Time information */}
          <div className="flex items-start">
            <Clock className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
            <div>
              <div className="text-sm text-gray-800 dark:text-gray-200">
                {isAllDay ? (
                  <span>
                    All day
                    {isMultiDay ? ` · ${formattedStart} - ${formattedEnd}` : ` · ${formattedStart}`}
                  </span>
                ) : (
                  <span>
                    {formattedStart}
                    {formattedEnd ? ` - ${formattedEnd}` : ''}
                  </span>
                )}
              </div>
              {timeZone && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{timeZone}</div>
              )}
            </div>
          </div>

          {/* Location if available */}
          {location && (
            <div className="flex items-start">
              <MapPin className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-gray-800 dark:text-gray-200">{location}</span>
            </div>
          )}

          {/* Attendees if available */}
          {attendees && attendees.length > 0 && (
            <div className="flex items-start">
              <Users className="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-800 dark:text-gray-200">
                {Array.isArray(attendees) ? (
                  <div className="space-y-1">
                    {attendees.map((attendee: string, index: number) => (
                      <div key={index}>{attendee}</div>
                    ))}
                  </div>
                ) : (
                  <div>{attendees}</div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Description if available */}
        {description && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              {description}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export { GoogleCalendarEventDisplay };
