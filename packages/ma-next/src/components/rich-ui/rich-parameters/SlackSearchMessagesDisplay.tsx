import React from 'react';
import { Search, SortAsc, SortDesc, Hash } from 'lucide-react';
import { SlackSearchMessagesInput } from 'src/config/nangoModels';

interface SlackSearchMessagesDisplayProps {
  parameters: SlackSearchMessagesInput;
}

/**
 * Renders a rich display of Slack search message parameters
 */
function SlackSearchMessagesDisplay({ parameters }: SlackSearchMessagesDisplayProps) {
  if (!parameters) {
    return null;
  }

  const { query, sort, sort_dir, count, page } = parameters;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <Search className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Message Search Parameters
          </h3>
        </div>
      </div>

      <div className="p-5 space-y-4">
        {/* Search Query */}
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Search Query
          </label>
          <div className="flex items-center">
            <Search className="w-4 h-4 text-gray-400 mr-2" />
            <span className="text-sm text-gray-900 dark:text-white font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              {query}
            </span>
          </div>
        </div>

        {/* Sort Options */}
        {(sort || sort_dir) && (
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Sort Options
            </label>
            <div className="flex items-center space-x-3">
              {sort && (
                <div className="flex items-center">
                  <Hash className="w-4 h-4 text-gray-400 mr-1" />
                  <span className="text-sm text-gray-900 dark:text-white">
                    Sort by: {sort}
                  </span>
                </div>
              )}
              {sort_dir && (
                <div className="flex items-center">
                  {sort_dir === 'asc' ? (
                    <SortAsc className="w-4 h-4 text-gray-400 mr-1" />
                  ) : (
                    <SortDesc className="w-4 h-4 text-gray-400 mr-1" />
                  )}
                  <span className="text-sm text-gray-900 dark:text-white">
                    {sort_dir === 'asc' ? 'Ascending' : 'Descending'}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pagination */}
        {(count || page) && (
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Pagination
            </label>
            <div className="flex items-center space-x-3 text-sm text-gray-900 dark:text-white">
              {count && (
                <span>Results per page: {count}</span>
              )}
              {page && (
                <span>Page: {page}</span>
              )}
            </div>
          </div>
        )}

        {/* Search Tips */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Tips
          </label>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <div>• Use quotes for exact phrases: "exact phrase"</div>
            <div>• Search in specific channels: in:#channel-name</div>
            <div>• Search by user: from:@username</div>
            <div>• Search by date: after:2023-01-01 or before:2023-12-31</div>
            <div>• Search for files: has:link or has:file</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { SlackSearchMessagesDisplay };
