import React from 'react';
import { GoogleCalendarEventDeleteOutput } from 'src/config/nangoModels';
import { GoogleCalendarEventDisplay } from './GoogleCalendarEventDisplay';

type GoogleCalendarEventDeleteOutputDisplayProps = {
  output: GoogleCalendarEventDeleteOutput;
  context?: string;
};

/**
 * Renders a rich display of a Google Calendar event deletion result
 */
function GoogleCalendarEventDeleteOutputDisplay({ output, context }: GoogleCalendarEventDeleteOutputDisplayProps) {
  if (!output || !output.event) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500 dark:text-gray-400">No event data available</p>
      </div>
    );
  }

  // Pass the event data to the regular event display with delete context
  return <GoogleCalendarEventDisplay output={output.event} context="delete-event" />;
}

export { GoogleCalendarEventDeleteOutputDisplay };
