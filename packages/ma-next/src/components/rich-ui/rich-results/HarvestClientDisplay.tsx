import React from 'react';
import { Building, Mail, MapPin, CheckCircle, XCircle, Calendar } from 'lucide-react';
import { HarvestIcon } from 'components/icons/providers';
import { HarvestClient } from 'src/config/nangoModels';
import { format } from 'date-fns';

type HarvestClientDisplayProps = {
  output: HarvestClient;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Harvest client
 */
function HarvestClientDisplay({ output }: HarvestClientDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <HarvestIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No client data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  try {
    if (output.created_at) {
      formattedCreatedAt = format(new Date(output.created_at), 'MMM d, yyyy');
    }
    if (output.updated_at) {
      formattedUpdatedAt = format(new Date(output.updated_at), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedCreatedAt = output.created_at || '';
    formattedUpdatedAt = output.updated_at || '';
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <HarvestIcon className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Harvest Client
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {output.is_active ? (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <CheckCircle className="w-3 h-3 mr-1" />
                Active
              </span>
            ) : (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                <XCircle className="w-3 h-3 mr-1" />
                Inactive
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-5">
        <div className="space-y-4">
          {/* Client Name and ID */}
          <div className="flex items-start">
            <Building className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-3 mt-1 flex-shrink-0" />
            <div className="flex-grow">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{output.name}</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">Client ID: {output.id}</p>
            </div>
          </div>

          {/* Address */}
          {output.address && (
            <div className="flex items-start">
              <MapPin className="w-5 h-5 text-gray-400 dark:text-gray-500 mr-3 mt-1 flex-shrink-0" />
              <div>
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Address</h5>
                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-line">{output.address}</p>
              </div>
            </div>
          )}

          {/* Currency and Statement Key */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {output.currency && (
              <div>
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Currency</h5>
                <p className="text-sm text-gray-900 dark:text-white font-mono bg-gray-50 dark:bg-gray-800 px-2 py-1 rounded">
                  {output.currency}
                </p>
              </div>
            )}

            {output.statement_key && (
              <div>
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Statement Key</h5>
                <p className="text-sm text-gray-900 dark:text-white font-mono bg-gray-50 dark:bg-gray-800 px-2 py-1 rounded">
                  {output.statement_key}
                </p>
              </div>
            )}
          </div>

          {/* Timestamps */}
          {(formattedCreatedAt || formattedUpdatedAt) && (
            <div>
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                Timestamps
              </h5>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  {formattedCreatedAt && (
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Created:</span>
                      <p className="text-gray-900 dark:text-white font-medium">{formattedCreatedAt}</p>
                    </div>
                  )}
                  {formattedUpdatedAt && (
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Updated:</span>
                      <p className="text-gray-900 dark:text-white font-medium">{formattedUpdatedAt}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { HarvestClientDisplay };
