import React from 'react';
import { HarvestIcon } from '../../icons/providers/HarvestIcon';

function HarvestDeleteProjectDisplay({ output }: { 
  output: any; 
  actionParameters?: Record<string, any>; 
  context?: string 
}) {
  return (
    <div className="flex items-start space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
      <HarvestIcon className="w-6 h-6 mt-1 flex-shrink-0" />
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2 mb-2">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Project Deletion
          </h3>
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            output.success 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' 
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
          }`}>
            {output.success ? 'Success' : 'Failed'}
          </div>
        </div>
        
        <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
          {output.message}
        </p>
      </div>
    </div>
  );
}

export { HarvestDeleteProjectDisplay };
