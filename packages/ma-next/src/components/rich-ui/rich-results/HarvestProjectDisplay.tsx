import React from 'react';
import { <PERSON>older<PERSON><PERSON>, Building, CheckCircle, XCircle, DollarSign, Calendar, AlertCircle } from 'lucide-react';
import { HarvestIcon } from 'components/icons/providers';
import { HarvestProject } from 'src/config/nangoModels';
import { format } from 'date-fns';

type HarvestProjectDisplayProps = {
  output: HarvestProject;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Harvest project
 */
function HarvestProjectDisplay({ output }: HarvestProjectDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <HarvestIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No project data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  let formattedStartsOn = '';
  let formattedEndsOn = '';
  try {
    if (output.created_at) {
      formattedCreatedAt = format(new Date(output.created_at), 'MMM d, yyyy');
    }
    if (output.updated_at) {
      formattedUpdatedAt = format(new Date(output.updated_at), 'MMM d, yyyy');
    }
    if (output.starts_on) {
      formattedStartsOn = format(new Date(output.starts_on), 'MMM d, yyyy');
    }
    if (output.ends_on) {
      formattedEndsOn = format(new Date(output.ends_on), 'MMM d, yyyy');
    }
  } catch (_e) {
    formattedCreatedAt = output.created_at || '';
    formattedUpdatedAt = output.updated_at || '';
    formattedStartsOn = output.starts_on || '';
    formattedEndsOn = output.ends_on || '';
  }

  // Budget calculations
  const budgetUsedPercentage = output.budget && output.over_budget_notification_percentage
    ? output.over_budget_notification_percentage
    : null;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <HarvestIcon className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Harvest Project
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {output.is_active ? (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <CheckCircle className="w-3 h-3 mr-1" />
                Active
              </span>
            ) : (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                <XCircle className="w-3 h-3 mr-1" />
                Inactive
              </span>
            )}
            {output.is_billable && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                <DollarSign className="w-3 h-3 mr-1" />
                Billable
              </span>
            )}
            {output.is_fixed_fee && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                Fixed Fee
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-5">
        <div className="space-y-4">
          {/* Project Name and Code */}
          <div className="flex items-start">
            <FolderOpen className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-3 mt-1 flex-shrink-0" />
            <div className="flex-grow">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{output.name}</h4>
              <div className="flex items-center space-x-4 mt-1">
                <p className="text-sm text-gray-500 dark:text-gray-400">Project ID: {output.id}</p>
                {output.code && (
                  <p className="text-sm text-gray-500 dark:text-gray-400">Code: {output.code}</p>
                )}
              </div>
            </div>
          </div>

          {/* Client */}
          {output.client && (
            <div className="flex items-start">
              <Building className="w-5 h-5 text-gray-400 dark:text-gray-500 mr-3 mt-1 flex-shrink-0" />
              <div>
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Client</h5>
                <p className="text-sm text-gray-900 dark:text-white font-medium">{output.client.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Currency: {output.client.currency}</p>
              </div>
            </div>
          )}

          {/* Project Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Billing Configuration</h5>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Bill by:</span>
                  <span className="text-sm text-gray-900 dark:text-white capitalize">{output.bill_by}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Budget by:</span>
                  <span className="text-sm text-gray-900 dark:text-white capitalize">{output.budget_by}</span>
                </div>
                {output.hourly_rate && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Hourly rate:</span>
                    <span className="text-sm text-gray-900 dark:text-white">${output.hourly_rate}/hr</span>
                  </div>
                )}
                {output.fee && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Fixed fee:</span>
                    <span className="text-sm text-gray-900 dark:text-white">${output.fee}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Budget Information */}
            {(output.budget || output.cost_budget) && (
              <div>
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Budget</h5>
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md space-y-2">
                  {output.budget && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Hours budget:</span>
                      <span className="text-sm text-gray-900 dark:text-white">{output.budget}h</span>
                    </div>
                  )}
                  {output.cost_budget && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Cost budget:</span>
                      <span className="text-sm text-gray-900 dark:text-white">${output.cost_budget}</span>
                    </div>
                  )}
                  {output.budget_is_monthly && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Budget type:</span>
                      <span className="text-sm text-gray-900 dark:text-white">Monthly</span>
                    </div>
                  )}
                  {output.notify_when_over_budget && (
                    <div className="flex items-center text-sm text-orange-600 dark:text-orange-400">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      <span>Over-budget notifications enabled</span>
                      {output.over_budget_notification_percentage && (
                        <span className="ml-1">({output.over_budget_notification_percentage}%)</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Project Timeline */}
          {(formattedStartsOn || formattedEndsOn) && (
            <div>
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                Project Timeline
              </h5>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {formattedStartsOn && (
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Starts:</span>
                      <p className="text-sm text-gray-900 dark:text-white font-medium">{formattedStartsOn}</p>
                    </div>
                  )}
                  {formattedEndsOn && (
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Ends:</span>
                      <p className="text-sm text-gray-900 dark:text-white font-medium">{formattedEndsOn}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {output.notes && (
            <div>
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</h5>
              <p className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-md whitespace-pre-line">
                {output.notes}
              </p>
            </div>
          )}

          {/* Timestamps */}
          {(formattedCreatedAt || formattedUpdatedAt) && (
            <div>
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                Timestamps
              </h5>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  {formattedCreatedAt && (
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Created:</span>
                      <p className="text-gray-900 dark:text-white font-medium">{formattedCreatedAt}</p>
                    </div>
                  )}
                  {formattedUpdatedAt && (
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Updated:</span>
                      <p className="text-gray-900 dark:text-white font-medium">{formattedUpdatedAt}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { HarvestProjectDisplay };
