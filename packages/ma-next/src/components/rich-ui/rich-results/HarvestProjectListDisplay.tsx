import React from 'react';
import { HarvestIcon } from '../../icons/providers/HarvestIcon';

function HarvestProjectListDisplay({ output }: {
  output: any;
  actionParameters?: Record<string, any>;
  context?: string
}) {
  const data = output;

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount?: number | null, currency?: string) => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount);
  };

  if (!data || !data.projects) {
    return (
      <div className="p-6 text-center">
        <HarvestIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No project data available</p>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
      <div className="flex items-center space-x-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <HarvestIcon className="w-6 h-6 flex-shrink-0" />
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Harvest Projects ({data.total_entries})
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Page {data.page || 1} of {data.total_pages} • {data.per_page} per page
          </p>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {data.projects.map((project: any) => (
          <div key={project.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-3 mb-2">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                    {project.name}
                  </h4>
                  {project.code && (
                    <span className="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                      {project.code}
                    </span>
                  )}
                </div>

                <div className="flex flex-wrap gap-2 mb-3">
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    project.is_active
                      ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                  }`}>
                    {project.is_active ? 'Active' : 'Inactive'}
                  </div>
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    project.is_billable
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                  }`}>
                    {project.is_billable ? 'Billable' : 'Non-billable'}
                  </div>
                  {project.is_fixed_fee && (
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300">
                      Fixed Fee
                    </div>
                  )}
                </div>

                <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <span className="font-medium">Client:</span> {project.client?.name}
                  {project.client?.currency && (
                    <span className="ml-2 text-gray-500 dark:text-gray-400">({project.client.currency})</span>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div className="space-y-2">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">ID:</span> {project.id}
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Bill By:</span> {project.bill_by}
                </div>
                {project.hourly_rate && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Hourly Rate:</span> {formatCurrency(project.hourly_rate, project.client?.currency)}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                {project.budget && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Budget:</span> {formatCurrency(project.budget, project.client?.currency)}
                    <div className="text-xs text-gray-500 dark:text-gray-400">By {project.budget_by}</div>
                  </div>
                )}
                {project.fee && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Fee:</span> {formatCurrency(project.fee, project.client?.currency)}
                  </div>
                )}
                {project.budget_is_monthly && (
                  <div className="text-xs text-blue-600 dark:text-blue-400">Monthly Budget</div>
                )}
              </div>

              <div className="space-y-2">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Created:</span> {formatDate(project.created_at)}
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Updated:</span> {formatDate(project.updated_at)}
                </div>
                {project.starts_on && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Start Date:</span> {formatDate(project.starts_on)}
                  </div>
                )}
                {project.ends_on && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">End Date:</span> {formatDate(project.ends_on)}
                  </div>
                )}
              </div>
            </div>

            {project.notes && (
              <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded text-sm">
                <span className="font-medium text-gray-700 dark:text-gray-300">Notes:</span>
                <p className="mt-1 text-gray-600 dark:text-gray-400">{project.notes}</p>
              </div>
            )}

            {(project.notify_when_over_budget || project.over_budget_notification_percentage) && (
              <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded text-sm">
                <div className="text-yellow-800 dark:text-yellow-200">
                  <span className="font-medium">Budget Notifications:</span>
                  {project.notify_when_over_budget && (
                    <span className="ml-2">Enabled</span>
                  )}
                  {project.over_budget_notification_percentage && (
                    <span className="ml-2">at {project.over_budget_notification_percentage}%</span>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {data.total_pages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex justify-between items-center">
            <div>
              Showing {((data.page || 1) - 1) * data.per_page + 1} to{' '}
              {Math.min((data.page || 1) * data.per_page, data.total_entries)} of{' '}
              {data.total_entries} projects
            </div>
            <div className="flex space-x-1">
              {data.previous_page && (
                <span className="text-blue-600 dark:text-blue-400">← Previous</span>
              )}
              {data.next_page && (
                <span className="text-blue-600 dark:text-blue-400">Next →</span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export { HarvestProjectListDisplay };
