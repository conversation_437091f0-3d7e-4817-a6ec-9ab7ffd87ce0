import React from 'react';
import { HarvestIcon } from '../../icons/providers/HarvestIcon';

function HarvestProjectTaskListDisplay({ output }: {
  output: any;
  actionParameters?: Record<string, any>;
  context?: string
}) {
  const data = output;

  if (!data || !data.task_assignments) {
    return (
      <div className="p-6 text-center">
        <HarvestIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No project task data available</p>
      </div>
    );
  }

  const formatCurrency = (amount?: number | null, currency?: string) => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
      <div className="flex items-center space-x-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <HarvestIcon className="w-6 h-6 flex-shrink-0" />
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Project Task Assignments ({data.total_entries})
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Page {data.page || 1} of {data.total_pages} • {data.per_page} per page
          </p>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {data.task_assignments.map((assignment: any) => (
          <div key={assignment.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-3 mb-2">
                  <h4 className="text-base font-medium text-gray-900 dark:text-white">
                    {assignment.task?.name || 'Unnamed Task'}
                  </h4>
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    assignment.is_active
                      ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                  }`}>
                    {assignment.is_active ? 'Active' : 'Inactive'}
                  </div>
                </div>

                {assignment.project && (
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <span className="font-medium">Project:</span> {assignment.project.name}
                    {assignment.project.code && (
                      <span className="ml-2 text-gray-500 dark:text-gray-400">({assignment.project.code})</span>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div className="space-y-2">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Assignment ID:</span> {assignment.id}
                </div>
                {assignment.task?.id && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Task ID:</span> {assignment.task.id}
                  </div>
                )}
                {assignment.project?.id && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Project ID:</span> {assignment.project.id}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  assignment.billable
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                }`}>
                  {assignment.billable ? 'Billable' : 'Non-billable'}
                </div>
                {assignment.hourly_rate && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Hourly Rate:</span> {formatCurrency(assignment.hourly_rate)}
                  </div>
                )}
                {assignment.budget && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Budget:</span> {formatCurrency(assignment.budget)}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Created:</span> {formatDate(assignment.created_at)}
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Updated:</span> {formatDate(assignment.updated_at)}
                </div>
              </div>
            </div>

            {(assignment.project?.is_active !== undefined || assignment.project?.is_billable !== undefined) && (
              <div className="mt-3 flex space-x-2">
                {assignment.project?.is_active !== undefined && (
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    assignment.project.is_active
                      ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                  }`}>
                    Project: {assignment.project.is_active ? 'Active' : 'Inactive'}
                  </div>
                )}
                {assignment.project?.is_billable !== undefined && (
                  <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    assignment.project.is_billable
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                  }`}>
                    Project: {assignment.project.is_billable ? 'Billable' : 'Non-billable'}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {data.total_pages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex justify-between items-center">
            <div>
              Showing {((data.page || 1) - 1) * data.per_page + 1} to{' '}
              {Math.min((data.page || 1) * data.per_page, data.total_entries)} of{' '}
              {data.total_entries} task assignments
            </div>
            <div className="flex space-x-1">
              {data.previous_page && (
                <span className="text-blue-600 dark:text-blue-400">← Previous</span>
              )}
              {data.next_page && (
                <span className="text-blue-600 dark:text-blue-400">Next →</span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export { HarvestProjectTaskListDisplay };
