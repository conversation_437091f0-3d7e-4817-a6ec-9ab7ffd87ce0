import React from 'react';
import { HarvestIcon } from '../../icons/providers/HarvestIcon';

function HarvestTaskListDisplay({
  output,
}: {
  output: any;
  actionParameters?: Record<string, any>;
  context?: string;
}) {
  const data = output;

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount?: number | null) => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (!data || !data.tasks) {
    return (
      <div className="p-6 text-center">
        <HarvestIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No task data available</p>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
      <div className="flex items-center space-x-3 p-4 border-b border-gray-200 dark:border-gray-700">
        <HarvestIcon className="w-6 h-6 flex-shrink-0" />
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Harvest Tasks ({data.total_entries})
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Page {data.page || 1} of {data.total_pages} • {data.per_page} per page
          </p>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {data.tasks.map((task: any) => (
          <div key={task.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-3 mb-2">
                  <h4 className="text-base font-medium text-gray-900 dark:text-white truncate">{task.name}</h4>
                  <div
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      task.is_active 
                        ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300' 
                        : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                    }`}
                  >
                    {task.is_active ? 'Active' : 'Inactive'}
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div className="space-y-2">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">ID:</span> {task.id}
                </div>
                <div
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    task.billable_by_default
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                  }`}
                >
                  {task.billable_by_default ? 'Billable by Default' : 'Non-billable by Default'}
                </div>
              </div>

              <div className="space-y-2">
                {task.default_hourly_rate && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Default Rate:</span>{' '}
                    {formatCurrency(task.default_hourly_rate)}
                  </div>
                )}
                <div
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    task.is_default 
                      ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-300' 
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                  }`}
                >
                  {task.is_default ? 'Default Task' : 'Custom Task'}
                </div>
              </div>

              <div className="space-y-2">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Created:</span>{' '}
                  {formatDate(task.created_at)}
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Updated:</span>{' '}
                  {formatDate(task.updated_at)}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {data.total_pages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex justify-between items-center">
            <div>
              Showing {((data.page || 1) - 1) * data.per_page + 1} to{' '}
              {Math.min((data.page || 1) * data.per_page, data.total_entries)} of{' '}
              {data.total_entries} tasks
            </div>
            <div className="flex space-x-1">
              {data.previous_page && <span className="text-blue-600 dark:text-blue-400">← Previous</span>}
              {data.next_page && <span className="text-blue-600 dark:text-blue-400">Next →</span>}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export { HarvestTaskListDisplay };
