import React from 'react';
import { Clock, User, Building, FolderOpen, CheckCircle, Play, Pause, DollarSign, Calendar } from 'lucide-react';
import { HarvestIcon } from 'components/icons/providers';
import { HarvestTimeEntry } from 'src/config/nangoModels';
import { format } from 'date-fns';

type HarvestTimeEntryDisplayProps = {
  output: HarvestTimeEntry;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Harvest time entry
 */
function HarvestTimeEntryDisplay({ output }: HarvestTimeEntryDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <HarvestIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No time entry data available</p>
      </div>
    );
  }

  // Format dates and times
  let formattedDate = '';
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  try {
    if (output.spent_date) {
      formattedDate = format(new Date(output.spent_date), 'MMM d, yyyy');
    }
    if (output.created_at) {
      formattedCreatedAt = format(new Date(output.created_at), 'MMM d, yyyy h:mm a');
    }
    if (output.updated_at) {
      formattedUpdatedAt = format(new Date(output.updated_at), 'MMM d, yyyy h:mm a');
    }
  } catch (e) {
    formattedDate = output.spent_date || '';
    formattedCreatedAt = output.created_at || '';
    formattedUpdatedAt = output.updated_at || '';
  }

  // Format hours
  const hoursDisplay = output.hours ? `${output.hours}h` : '0h';
  const isRunning = output.is_running;
  const isBillable = output.billable !== false; // Default to true if not specified

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <HarvestIcon className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Harvest Time Entry #{output.id}
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {isRunning && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <Play className="w-3 h-3 mr-1" />
                Running
              </span>
            )}
            {output.is_locked && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                Locked
              </span>
            )}
            {isBillable && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                <DollarSign className="w-3 h-3 mr-1" />
                Billable
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-5">
        <div className="space-y-4">
          {/* Time and Date */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-3" />
              <div>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{hoursDisplay}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Time tracked</p>
              </div>
            </div>
            {formattedDate && (
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <Calendar className="w-4 h-4 mr-1" />
                {formattedDate}
              </div>
            )}
          </div>

          {/* Notes */}
          {output.notes && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                {output.notes}
              </p>
            </div>
          )}

          {/* Project and Task */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Project */}
            {output.project && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <FolderOpen className="w-4 h-4 mr-1" />
                  Project
                </h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                  <p className="font-medium text-gray-900 dark:text-white">{output.project.name}</p>
                  {output.project.code && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">Code: {output.project.code}</p>
                  )}
                  <div className="flex items-center mt-1 space-x-2">
                    {output.project.is_active && (
                      <span className="inline-flex items-center text-xs text-green-600 dark:text-green-400">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Active
                      </span>
                    )}
                    {output.project.is_billable && (
                      <span className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400">
                        <DollarSign className="w-3 h-3 mr-1" />
                        Billable
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Task */}
            {output.task && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Task</h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                  <p className="font-medium text-gray-900 dark:text-white">{output.task.name}</p>
                  {output.task.default_hourly_rate && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Rate: ${output.task.default_hourly_rate}/hr
                    </p>
                  )}
                  {output.task.billable_by_default && (
                    <span className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 mt-1">
                      <DollarSign className="w-3 h-3 mr-1" />
                      Billable by default
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Client and User */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Client */}
            {output.client && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Building className="w-4 h-4 mr-1" />
                  Client
                </h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                  <p className="font-medium text-gray-900 dark:text-white">{output.client.name}</p>
                  {output.client.currency && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">Currency: {output.client.currency}</p>
                  )}
                </div>
              </div>
            )}

            {/* User */}
            {output.user && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  User
                </h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                  <p className="font-medium text-gray-900 dark:text-white">{output.user.name}</p>
                  {output.user.email && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">{output.user.email}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Billing Information */}
          {(output.billable_rate || output.cost_rate) && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Billing Information</h4>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {output.billable_rate && (
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Billable Rate</p>
                      <p className="font-medium text-gray-900 dark:text-white">${output.billable_rate}/hr</p>
                    </div>
                  )}
                  {output.cost_rate && (
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Cost Rate</p>
                      <p className="font-medium text-gray-900 dark:text-white">${output.cost_rate}/hr</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Timestamps */}
          {(formattedCreatedAt || formattedUpdatedAt) && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                Timestamps
              </h4>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  {formattedCreatedAt && (
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Created:</span>
                      <p className="text-gray-900 dark:text-white font-medium">{formattedCreatedAt}</p>
                    </div>
                  )}
                  {formattedUpdatedAt && (
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">Updated:</span>
                      <p className="text-gray-900 dark:text-white font-medium">{formattedUpdatedAt}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { HarvestTimeEntryDisplay };
