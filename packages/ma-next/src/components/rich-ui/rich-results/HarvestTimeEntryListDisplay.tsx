import React from 'react';
import { Clock, ChevronRight, Calendar, Users } from 'lucide-react';
import { HarvestIcon } from 'components/icons/providers';
import { HarvestTimeEntryList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type HarvestTimeEntryListDisplayProps = {
  output: HarvestTimeEntryList;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Harvest time entry list
 */
function HarvestTimeEntryListDisplay({ output }: HarvestTimeEntryListDisplayProps) {
  if (!output || !output.time_entries || output.time_entries.length === 0) {
    return (
      <div className="p-6 text-center">
        <HarvestIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No time entries found</p>
      </div>
    );
  }

  const { time_entries, total_entries, page, per_page, total_pages } = output;

  // Calculate total hours across all entries
  const totalHours = time_entries.reduce((sum, entry) => sum + (entry.hours || 0), 0);

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <HarvestIcon className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-2" />
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
              Time Entries ({total_entries || time_entries.length})
            </h3>
          </div>
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <Clock className="w-4 h-4 mr-1" />
            <span>{totalHours.toFixed(1)}h total</span>
          </div>
        </div>
        
        {/* Pagination info */}
        {total_pages && total_pages > 1 && (
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Page {page || 1} of {total_pages} • {per_page} per page
          </div>
        )}
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {time_entries.map((entry, index) => {
          // Format date
          let formattedDate = '';
          try {
            if (entry.spent_date) {
              formattedDate = format(new Date(entry.spent_date), 'MMM d, yyyy');
            }
          } catch (e) {
            formattedDate = entry.spent_date || '';
          }

          const hoursDisplay = entry.hours ? `${entry.hours}h` : '0h';
          const isRunning = entry.is_running;
          const isBillable = entry.billable !== false;

          return (
            <div key={entry.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800">
              <div className="flex items-center justify-between">
                <div className="flex-grow min-w-0">
                  <div className="flex items-center space-x-3">
                    {/* Time and running status */}
                    <div className="flex items-center">
                      <Clock className={`w-4 h-4 mr-1 ${isRunning ? 'text-green-500' : 'text-orange-600 dark:text-orange-400'}`} />
                      <span className={`font-medium ${isRunning ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'}`}>
                        {hoursDisplay}
                      </span>
                      {isRunning && (
                        <span className="ml-1 text-xs text-green-600 dark:text-green-400">(running)</span>
                      )}
                    </div>

                    {/* Date */}
                    {formattedDate && (
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formattedDate}
                      </div>
                    )}

                    {/* Billable indicator */}
                    {isBillable && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                        Billable
                      </span>
                    )}

                    {/* Locked indicator */}
                    {entry.is_locked && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                        Locked
                      </span>
                    )}
                  </div>

                  {/* Project and Task */}
                  <div className="mt-2 flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    {entry.project && (
                      <>
                        <span className="font-medium text-gray-900 dark:text-white">{entry.project.name}</span>
                        {entry.project.code && (
                          <span className="text-gray-500 dark:text-gray-400">({entry.project.code})</span>
                        )}
                      </>
                    )}
                    
                    {entry.task && entry.project && <ChevronRight className="w-3 h-3" />}
                    
                    {entry.task && (
                      <span>{entry.task.name}</span>
                    )}
                  </div>

                  {/* Client and User */}
                  <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                    {entry.client && (
                      <div className="flex items-center">
                        <Users className="w-3 h-3 mr-1" />
                        <span>{entry.client.name}</span>
                      </div>
                    )}
                    {entry.user && (
                      <span>{entry.user.name}</span>
                    )}
                  </div>

                  {/* Notes */}
                  {entry.notes && (
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 truncate">
                      {entry.notes}
                    </p>
                  )}
                </div>

                {/* Entry ID */}
                <div className="flex-shrink-0 ml-4 text-xs text-gray-400 dark:text-gray-500">
                  #{entry.id}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer with pagination summary */}
      {time_entries.length > 0 && (
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
            <span>Showing {time_entries.length} entries</span>
            {total_entries && total_entries > time_entries.length && (
              <span>{total_entries - time_entries.length} more entries available</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export { HarvestTimeEntryListDisplay };
