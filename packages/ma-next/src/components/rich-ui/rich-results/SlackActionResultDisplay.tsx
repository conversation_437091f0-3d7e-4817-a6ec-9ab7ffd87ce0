import React from 'react';
import { Check, X, Alert<PERSON>ir<PERSON>, <PERSON>, Trash2, <PERSON> } from 'lucide-react';

interface SlackActionResult {
  ok: boolean;
  error?: string;
  permalink?: string;
  channel?: string;
  ts?: string;
  name?: string; // For reaction name
}

interface SlackActionResultDisplayProps {
  output: SlackActionResult;
  actionType: 'reaction' | 'delete' | 'permalink';
  actionParameters?: Record<string, any>;
}

/**
 * Generic component for displaying Slack action results (reactions, deletes, permalinks)
 */
function SlackActionResultDisplay({ output, actionType, actionParameters }: SlackActionResultDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No result data available</p>
      </div>
    );
  }

  // Handle error response format from Nango actions
  if ('error' in output && typeof output.error === 'object' && output.error !== null) {
    const errorObj = output.error as { status: number; message: string };
    return (
      <div className="p-6 text-center">
        <AlertCircle className="w-12 h-12 mx-auto text-red-400 dark:text-red-600 mb-3" />
        <p className="text-red-600 dark:text-red-400 mb-2">Action failed</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">{errorObj.message}</p>
      </div>
    );
  }

  const isSuccess = output.ok && !output.error;

  // Get appropriate icon and title based on action type
  const getActionInfo = () => {
    switch (actionType) {
      case 'reaction':
        return {
          icon: Heart,
          title: 'Add Reaction',
          successMessage: `Reaction ${actionParameters?.name || 'added'} successfully`,
          failureMessage: 'Failed to add reaction'
        };
      case 'delete':
        return {
          icon: Trash2,
          title: 'Delete Message',
          successMessage: 'Message deleted successfully',
          failureMessage: 'Failed to delete message'
        };
      case 'permalink':
        return {
          icon: Link,
          title: 'Get Permalink',
          successMessage: 'Permalink generated successfully',
          failureMessage: 'Failed to generate permalink'
        };
      default:
        return {
          icon: AlertCircle,
          title: 'Slack Action',
          successMessage: 'Action completed successfully',
          failureMessage: 'Action failed'
        };
    }
  };

  const actionInfo = getActionInfo();
  const IconComponent = actionInfo.icon;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <IconComponent className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {actionInfo.title}
          </h3>
          {isSuccess ? (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              <Check className="w-3 h-3 mr-1" />
              Success
            </span>
          ) : (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
              <X className="w-3 h-3 mr-1" />
              Failed
            </span>
          )}
        </div>
      </div>

      <div className="p-5">
        {/* Status message */}
        <div className="mb-1">
          <p className={`text-sm ${isSuccess ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}`}>
            {isSuccess ? actionInfo.successMessage : actionInfo.failureMessage}
          </p>
          {output.error && (
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">
              Error: {output.error}
            </p>
          )}
        </div>

        {/* Action-specific details */}
        <div className="space-y-3">
          {/* Permalink specific */}
          {actionType === 'permalink' && output.permalink && (
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Message Link
              </label>
              <div className="flex items-center">
                <input
                  type="text"
                  value={output.permalink}
                  readOnly
                  className="flex-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md px-3 py-2 text-gray-900 dark:text-white"
                />
                <button
                  onClick={() => navigator.clipboard.writeText(output.permalink!)}
                  className="ml-2 px-3 py-2 text-xs bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                  title="Copy to clipboard"
                >
                  Copy
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { SlackActionResultDisplay };
