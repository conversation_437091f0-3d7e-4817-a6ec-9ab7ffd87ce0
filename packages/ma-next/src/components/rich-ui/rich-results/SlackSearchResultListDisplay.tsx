import React, { useState } from 'react';
import { Search, MessageSquare, AlertCircle, Loader2 } from 'lucide-react';
import { SlackSearchResultList } from 'src/config/nangoModels';

interface SlackSearchResultListDisplayProps {
  output: SlackSearchResultList;
  actionParameters?: Record<string, any>;
}

/**
 * Renders a rich display of Slack search results with inline search functionality
 */
function SlackSearchResultListDisplay({ output, actionParameters }: SlackSearchResultListDisplayProps) {
  const [searchQuery, setSearchQuery] = useState(actionParameters?.query || '');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState(output);

  const data = searchResults || output;

  // Handle search submission
  const handleSearch = async () => {
    if (!searchQuery.trim() || isSearching) return;

    setIsSearching(true);
    try {
      // Call perform-action with new search parameters
      const response = await fetch('/netlify/functions/perform-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          providerKey: 'slack',
          actionKey: 'search-messages',
          actionParameters: {
            ...actionParameters,
            query: searchQuery,
          },
        }),
      });

      if (response.ok) {
        const newResults = await response.json();
        setSearchResults(newResults);
      }
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Check if we have valid data
  if (!data) {
    return (
      <div className="p-6 text-center">
        <Search className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No search results available</p>
      </div>
    );
  }

  const isSuccess = data.ok && !data.error;
  const messages = data.messages || {};
  const messageList = Array.isArray(messages.matches) ? messages.matches : [];

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center mb-3">
          <Search className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Message Search
          </h3>
          {isSuccess && messageList.length > 0 && (
            <span className="ml-auto text-xs text-gray-500 dark:text-gray-400">
              {messageList.length} result{messageList.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>

        {/* Search input */}
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md
                       focus:ring-2 focus:ring-purple-500 focus:border-transparent
                       dark:bg-gray-700 dark:text-white"
              disabled={isSearching}
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={isSearching || !searchQuery.trim()}
            className="px-4 py-2 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700
                     disabled:opacity-50 disabled:cursor-not-allowed transition-colors
                     flex items-center space-x-1"
          >
            {isSearching ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Searching...</span>
              </>
            ) : (
              <span>Search</span>
            )}
          </button>
        </div>
      </div>

      {/* Error state */}
      {!isSuccess && (
        <div className="p-6 text-center">
          <AlertCircle className="w-12 h-12 mx-auto text-red-400 dark:text-red-600 mb-3" />
          <p className="text-red-600 dark:text-red-400 mb-2">Search failed</p>
          {data.error && (
            <p className="text-sm text-gray-500 dark:text-gray-400">{data.error}</p>
          )}
        </div>
      )}

      {/* No results */}
      {isSuccess && messageList.length === 0 && (
        <div className="p-6 text-center">
          <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
          <p className="text-gray-500 dark:text-gray-400">No messages found</p>
          {data.query && (
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
              for "{data.query}"
            </p>
          )}
        </div>
      )}

      {/* Search results */}
      {isSuccess && messageList.length > 0 && (
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {messageList.map((match: any, index: number) => (
            <SearchResultItem key={match.iid || index} match={match} />
          ))}
        </div>
      )}

    </div>
  );
}

// Component for individual search result items
interface SearchResultItemProps {
  match: any;
}

function SearchResultItem({ match }: SearchResultItemProps) {
  // Format timestamp
  let formattedTime = '';
  try {
    const timestamp = parseFloat(match.ts) * 1000;
    formattedTime = new Date(timestamp).toLocaleString();
  } catch {
    formattedTime = match.ts || '';
  }

  return (
    <div className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
            <MessageSquare className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          </div>
        </div>

        <div className="flex-1 min-w-0">
          {/* Header with user and channel info */}
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-1">
            {match.username && (
              <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">
                {match.username}
              </span>
            )}
            {match.channel?.name && (
              <span className="mr-2">
                in #{match.channel.name}
              </span>
            )}
            {formattedTime && <span>{formattedTime}</span>}
          </div>

          {/* Message text */}
          <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line">
            {match.text}
          </p>
        </div>
      </div>
    </div>
  );
}

export { SlackSearchResultListDisplay };
