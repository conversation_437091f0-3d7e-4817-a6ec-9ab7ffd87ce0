import React, { useMemo } from 'react';
import { MessageSquare } from 'lucide-react';
import { SlackSyncMessage } from 'src/config/nangoModels';
import { useSlackUsers } from 'components/rich-ui/rich-results/shared/slackUsers';
import { SlackMessage, extractUserIds } from './shared/SlackMessage';

interface SlackSyncMessageDisplayProps {
  output: SlackSyncMessage;
  actionParameters?: Record<string, any>;
}

/**
 * Renders a rich display of a single Slack sync message
 */
function SlackSyncMessageDisplay({ output, actionParameters }: SlackSyncMessageDisplayProps) {
  const data = output;

  const idsFromUserField = data?.message?.user ? [data.message.user] : [];
  const idsFromText = data?.message?.text ? extractUserIds(data.message.text) : [];
  const uniqueIds = useMemo(
    () => Array.from(new Set([...idsFromUser<PERSON>ield, ...idsFromText])),
    [output]
  );

  const users = useSlackUsers(uniqueIds);

  // Check if we have valid data
  if (!data || !data.message) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No message data available</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Message
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        <SlackMessage
          message={data.message}
          users={users}
        />
      </div>
    </div>
  );
}

export { SlackSyncMessageDisplay };
