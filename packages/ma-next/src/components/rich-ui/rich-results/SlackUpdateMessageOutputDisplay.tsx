import React from 'react';
import { MessageSquare, Check, X, Hash, User } from 'lucide-react';
import { SlackUpdateMessageOutput } from 'src/config/nangoModels';

type SlackUpdateMessageOutputDisplayProps = {
  output: SlackUpdateMessageOutput;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Slack message update result
 */
function SlackUpdateMessageOutputDisplay({ output, actionParameters }: SlackUpdateMessageOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No message update data available</p>
      </div>
    );
  }

  const isSuccess = output.ok;
  const messageText = actionParameters?.text || output.text || '';
  const channelName = actionParameters?.channel || output.channel || '';
  const error = output.error || '';

  // Format timestamp for display
  let formattedTime = '';
  try {
    if (output.ts) {
      const timestamp = parseFloat(output.ts) * 1000;
      formattedTime = new Date(timestamp).toLocaleString();
    }
  } catch {
    formattedTime = '';
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Message Updated
          </h3>
          {isSuccess ? (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              <Check className="w-3 h-3 mr-1" />
              Updated
            </span>
          ) : (
            <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
              <X className="w-3 h-3 mr-1" />
              Failed
            </span>
          )}
        </div>
      </div>

      {isSuccess && messageText ? (
        <div className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <div className="w-9 h-9 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                <User className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              {/* User and timestamp */}
              <div className="flex items-center mb-1">
                <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mr-2">
                  You
                </div>
                {formattedTime && (
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <span>{formattedTime}</span>
                  </div>
                )}
                {channelName && (
                  <div className="ml-auto flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <Hash className="w-3 h-3 mr-1" />
                    <span>{channelName}</span>
                  </div>
                )}
              </div>

              {/* Message text with edited indicator */}
              <div className="flex items-end">
                <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line flex-1">
                  {messageText}
                </p>
                <span className="text-xs text-gray-400 dark:text-gray-500 ml-2 italic">
                  (edited)
                </span>
              </div>
            </div>
          </div>
        </div>
      ) : !isSuccess ? (
        <div className="p-4">
          <div className="text-sm text-red-600 dark:text-red-400">
            Failed to update message
            {error && <span className="block mt-1">Error: {error}</span>}
          </div>
        </div>
      ) : null}
    </div>
  );
}

export { SlackUpdateMessageOutputDisplay };
