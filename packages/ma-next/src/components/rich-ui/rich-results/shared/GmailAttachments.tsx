import React from 'react';
import { ExternalLink, Paperclip } from 'lucide-react';
import { UrlAccessibleFile } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../../common/ContextMenu';

interface GmailAttachmentsProps {
  attachments: UrlAccessibleFile[];
}

function extractFilenameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop() || 'attachment';
    return decodeURIComponent(filename);
  } catch {
    return 'attachment';
  }
}

function GmailAttachments({ attachments }: GmailAttachmentsProps) {
  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div className="mt-3 space-y-2">
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Attachments ({attachments.length})
      </div>
      {attachments.map((attachment, index) => {
        const filename = extractFilenameFromUrl(attachment.url);

        const menuItems: ContextMenuItem[] = [
          {
            label: 'View File',
            onClick: () => window.open(attachment.url, '_blank'),
            icon: <ExternalLink className="w-4 h-4 mr-2" />,
          },
        ];

        return (
          <div
            key={index}
            className="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <Paperclip className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {filename}
                </p>
                <ContextMenu items={menuItems}>
                  <>
                    {/**
                     * This button is intentionally hidden for now but kept for potential
                     * future use.
                     */}
                    {/*
                    <button className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 ml-2">
                      <ExternalLink className="w-4 h-4" />
                    </button>
                    */}
                  </>
                </ContextMenu>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

export { GmailAttachments };
