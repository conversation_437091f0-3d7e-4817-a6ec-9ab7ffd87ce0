import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';

export interface AgentExecution {
  id: string;
  taskflowId: string;
  triggerData?: any;
  context: Record<string, any>;
  result?: any;
  status?: string;
  startedAt: string;
  updatedAt: string;
  completedAt?: string;
  isTest?: boolean;
}

function useAgentExecutions(taskflowId: string): {
  executions: AgentExecution[];
  loading: boolean;
  error: string | null;
} {
  const [executions, setExecutions] = useState<AgentExecution[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let subscription: any;

    const fetchExecutions = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all executions for this taskflow
        const { data, error } = await supabase
          .from('taskflow_executions')
          .select(`
            id,
            taskflowId,
            triggerData,
            context,
            result,
            status,
            startedAt,
            updatedAt,
            completedAt,
            isTest
          `)
          .eq('taskflowId', taskflowId)
          .order('updatedAt', { ascending: false });

        if (error) {
          throw error;
        }

        setExecutions(data || []);

        // Subscribe to real-time updates for this taskflow's executions
        const channelName = `agent-executions-${taskflowId}`;
        subscription = supabase
          .channel(channelName)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'taskflow_executions',
              filter: `taskflowId=eq.${taskflowId}`,
            },
            payload => {
              if (payload.eventType === 'INSERT') {
                setExecutions(prev => [payload.new as AgentExecution, ...prev]);
              } else if (payload.eventType === 'UPDATE') {
                setExecutions(prev =>
                  prev.map(exec =>
                    exec.id === payload.new.id ? { ...exec, ...payload.new } : exec
                  )
                );
              } else if (payload.eventType === 'DELETE') {
                setExecutions(prev => prev.filter(exec => exec.id !== payload.old.id));
              }
            }
          )
          .subscribe();
      } catch (err) {
        setError('Failed to load agent executions');
        console.error('Error fetching agent executions:', err);
      } finally {
        setLoading(false);
      }
    };

    if (taskflowId) {
      fetchExecutions();
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [taskflowId]);

  return {
    executions,
    loading,
    error,
  };
}

export { useAgentExecutions };
