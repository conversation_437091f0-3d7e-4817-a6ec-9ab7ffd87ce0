import { useChatContext } from 'providers/ChatContext';
import { useAgentsStore } from '../stores/agents';

/**
 * @hook useCurrentAgent - conversation agent (doesn't update real time)
 */
const useCurrentAgent = () => {
  const { currentTaskflowId } = useChatContext();
  const [{ agents: conversations }] = useAgentsStore();

  return conversations.find(a => a.id === currentTaskflowId)?.agent || undefined;
};

export { useCurrentAgent };
