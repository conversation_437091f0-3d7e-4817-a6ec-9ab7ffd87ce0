import { useEffect, useState } from 'react';
import { supabase } from 'lib/supabase';

/**
 * Custom hook to fetch taskflow data
 * @param taskflowId The ID of the taskflow to fetch
 * @param setTaskflow Function to set the taskflow data
 * @param setError Function to set any error that occurs
 */
function useTaskflow(taskflowId: string | null) {
  const [taskflow, setTaskflow] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTaskflow() {
      try {
        const { data, error } = await supabase
          .from('taskflows')
          .select('id, schema, testExecutionId, active')
          .eq('id', taskflowId)
          .single();

        if (error) {
          throw error;
        }

        setTaskflow(data);
      } catch (err: any) {
        console.error('Error fetching taskflow:', err);
        setError(err.message || 'Failed to load taskflow');
      }
    }

    if (taskflowId) {
      fetchTaskflow();
    }
  }, [taskflowId, setTaskflow, setError]);

  return { taskflow, setTaskflow, error };
}

export { useTaskflow };
