import { createStore } from './createStore';

export type ModalType =
  | 'login'
  | 'register'
  | 'example'
  | 'agentBrowse'
  | 'manageAgents'
  | 'connections'
  | 'agentExecutions'
  | 'none'
  | string;

export interface ModalState {
  activeModal: ModalType;
}

const initialState: ModalState = {
  activeModal: 'none',
};

class ModalStore {
  private static instance: ModalStore;
  private store;

  private constructor() {
    this.store = createStore<ModalState>(initialState);
  }

  static getInstance(): ModalStore {
    if (!ModalStore.instance) {
      ModalStore.instance = new ModalStore();
    }
    return ModalStore.instance;
  }

  openModal(modalType: ModalType) {
    this.store.setState({ activeModal: modalType });
  }

  closeModal() {
    this.store.setState({ activeModal: 'none' });
  }

  useModalState() {
    return this.store.useStoreState();
  }

  useIsModalOpen(modalType: ModalType) {
    return this.store.useStoreState(state => state.activeModal === modalType);
  }
}

const modalStore = ModalStore.getInstance();
export { modalStore };
