import { <PERSON><PERSON> } from '@mastra/core';
import { createLogger } from '@mastra/core/logger';
import { LibSQLStore } from '@mastra/libsql';
import { NetlifyDeployer } from '@mastra/deployer-netlify';
import { intentAgent } from './agents/intentAgent';
import { actionsAgent } from './agents/actionsAgent';
import { promptGeneratorAgent } from './agents/promptGenerator';
import { slackAgent } from './agents/slackAgent';
import { weatherWorkflow } from './workflows/weatherWorkflow';
import { playgroundAgent } from './agents/playgroundAgent';

const mastra = new Mastra({
  server: {
    port: 4111,
    host: '0.0.0.0',
  },
  workflows: { weatherWorkflow },
  agents: {
    intentAgent,
    actionsAgent,
    promptGeneratorAgent,
    slackAgent,
    playgroundAgent,
  },
  logger: createLogger({
    name: 'Mastra',
    level: 'info',
  }),
  storage: new LibSQLStore({
    url: 'file:../mastra.db',
  }),
  deployer: new NetlifyDeployer({
    scope: 'makeagent',
    projectName: 'mastra',
    token: 'nfp_hKN6A8UGHiG3NmdZ5suq6aZLpX4CS6gbbdac'
  }),
});

export { mastra };
