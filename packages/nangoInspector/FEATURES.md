# Actions Inspector Features

## Recently Added Features

### 1. Collapsible Parameters Section ✅

The parameters section is now collapsible using an accordion interface:

- **Auto-collapse after execution**: When an action is executed and a result is returned, the parameters section automatically collapses to give more focus to the results
- **Manual toggle**: Users can manually expand/collapse the parameters section by clicking on the header
- **Visual indicator**: A chevron icon shows the current state (expanded/collapsed)

**Implementation details:**
- Uses Radix UI Accordion component
- State managed with `parametersAccordionValue`
- Triggers collapse via `useEffect` when `lastResult` changes and execution is complete

### 2. Organized Result Data Management ✅

All execution results are now automatically saved to an organized directory structure:

#### Directory Structure
```
result-data/
├── actions/         # Results from direct action execution via UI
│   ├── github/
│   ├── google-calendar/
│   └── ...
├── scripts/         # Results from providerRunner.ts executions
│   ├── github/
│   ├── google-calendar/
│   └── ...
└── syncs/          # Results from sync operations (future)
    ├── provider/
    └── ...
```

#### Data Loading with Fallback
- **Primary**: Load from `actions/` directory
- **Fallback**: Load from `scripts/` directory if not found in actions
- **Error**: Return 404 if not found in either location

#### Saved File Format
All results use a consistent format:
```json
{
  "input": {
    "parameter1": "value1"
  },
  "output": {
    "result": "data"
  },
  "success": true,
  "error": null
}
```

**Implementation details:**
- Execute API saves to `result-data/actions/{provider}/{action}.json`
- ProviderRunner saves to `result-data/scripts/{provider}/{action}.json`
- Sample-data API loads with fallback: actions → scripts → 404
- Files are saved without timestamps for consistency
- Existing files migrated from old flat structure to new organized structure

## Usage

1. **Navigate to an action**: Select a provider and action from the left panel
2. **Set parameters**: Expand the parameters section (if collapsed) and configure your parameters
3. **Execute**: Click "Execute Action" button
4. **View results**: The parameters section will auto-collapse and results will be displayed
5. **Load previous results**: Previous execution results are automatically loaded when viewing an action

## File Locations

- **ActionPanel component**: `src/components/ActionPanel.tsx`
- **Execute API route**: `src/pages/api/execute.ts`
- **Sample-data API route**: `src/pages/api/sample-data/[...params].ts`
- **Provider runner**: `src/tests/providerRunner.ts`
- **Result data directory**: `result-data/`
- **UI components**: `src/components/ui/accordion.tsx`

## Migration Notes

- ✅ Existing files from old structure (`result-data/{provider}/`) have been migrated to new structure
- ✅ Old format files converted from `{provider, action, parameters, result}` to `{input, output, success, error}`
- ✅ Timestamp suffixes removed from filenames for consistency
- ✅ Old directories cleaned up after successful migration
