import { useState } from 'react';
import { ChevronDown, ChevronRight, Calendar, User, Hash, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface RecordsDisplayProps {
  records: any[] | any;
  className?: string;
}

export function RecordsDisplay({ records, className }: RecordsDisplayProps) {
  const [expandedRecords, setExpandedRecords] = useState<Set<number>>(new Set());

  const toggleRecord = (index: number) => {
    setExpandedRecords(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const renderRecordValue = (key: string, value: any): JSX.Element => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">null</span>;
    }

    if (typeof value === 'boolean') {
      return <span className={cn("px-2 py-1 rounded text-xs", value ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200")}>{value.toString()}</span>;
    }

    if (typeof value === 'number') {
      return <span className="font-mono text-blue-600 dark:text-blue-400">{value}</span>;
    }

    if (typeof value === 'string') {
      // Check if it's a date string
      if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
        try {
          const date = new Date(value);
          return (
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-sm">{date.toLocaleString()}</span>
            </div>
          );
        } catch {
          return <span className="text-sm">{value}</span>;
        }
      }

      // Check if it's a URL
      if (value.startsWith('http://') || value.startsWith('https://')) {
        return (
          <a href={value} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400  text-sm break-all">
            {value}
          </a>
        );
      }

      return <span className="text-sm break-words">{value}</span>;
    }

    if (typeof value === 'object' && value !== null) {
      return (
        <details className="mt-1">
          <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
            View object ({Object.keys(value).length} keys)
          </summary>
          <pre className="mt-1 text-xs bg-muted p-2 rounded overflow-auto max-h-32">
            {JSON.stringify(value, null, 2)}
          </pre>
        </details>
      );
    }

    return <span className="text-sm">{String(value)}</span>;
  };

  const getFieldIcon = (key: string) => {
    const lowerKey = key.toLowerCase();
    if (lowerKey.includes('id')) return <Hash className="h-3 w-3" />;
    if (lowerKey.includes('name') || lowerKey.includes('title') || lowerKey.includes('summary')) return <User className="h-3 w-3" />;
    if (lowerKey.includes('date') || lowerKey.includes('time') || lowerKey.includes('created') || lowerKey.includes('updated')) return <Clock className="h-3 w-3" />;
    return null;
  };

  const renderRecord = (record: any, index: number) => {
    const isExpanded = expandedRecords.has(index);
    const recordKeys = Object.keys(record);
    const importantKeys = recordKeys.filter(key =>
      ['id', 'name', 'title', 'summary', 'subject'].some(important =>
        key.toLowerCase().includes(important)
      )
    );
    const otherKeys = recordKeys.filter(key => !importantKeys.includes(key));

    return (
      <Card key={index} className="mb-3">
        <div
          className="p-4 cursor-pointer hover:bg-muted/50"
          onClick={() => toggleRecord(index)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                <span className="font-medium">Record {index + 1}</span>
              </div>

              {/* Show important fields in collapsed view */}
              {!isExpanded && importantKeys.length > 0 && (
                <div className="mt-2 space-y-1">
                  {importantKeys.slice(0, 2).map(key => (
                    <div key={key} className="flex items-center space-x-2 text-sm">
                      <span className="text-muted-foreground font-medium min-w-0 truncate">{key}:</span>
                      <div className="flex-1 min-w-0">
                        {renderRecordValue(key, record[key])}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {isExpanded && (
          <div className="px-4 pb-4">
            <Separator className="mb-4" />
            <div className="space-y-3">
              {/* Important fields first */}
              {importantKeys.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-2">Key Fields</h4>
                  <div className="grid gap-2">
                    {importantKeys.map(key => (
                      <div key={key} className="flex items-start space-x-2 p-2 bg-muted/30 rounded">
                        <div className="flex items-center space-x-1 min-w-0">
                          {getFieldIcon(key)}
                          <span className="text-sm font-medium text-muted-foreground truncate">{key}:</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          {renderRecordValue(key, record[key])}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Other fields */}
              {otherKeys.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-2">Other Fields</h4>
                  <div className="grid gap-2">
                    {otherKeys.map(key => (
                      <div key={key} className="flex items-start space-x-2 p-2 rounded">
                        <div className="flex items-center space-x-1 min-w-0">
                          {getFieldIcon(key)}
                          <span className="text-sm font-medium text-muted-foreground truncate">{key}:</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          {renderRecordValue(key, record[key])}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </Card>
    );
  };

  if (!records) {
    return null;
  }

  if (Array.isArray(records) && records.length > 0) {
    return (
      <div className={cn("space-y-2", className)}>
        {records.map((record, index) => renderRecord(record, index))}
      </div>
    );
  }

  if (records && typeof records === 'object' && !Array.isArray(records)) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="text-sm text-muted-foreground">Non-array result:</div>
        {renderRecord(records, 0)}
      </div>
    );
  }

  return (
    <div className={cn("text-center py-8", className)}>
      <div className="text-muted-foreground">No records found</div>
    </div>
  );
}
