import { cn } from "@/lib/utils";

interface ShimmerProps {
  className?: string;
}

export function Shimmer({ className }: ShimmerProps) {
  return (
    <div
      className={cn(
        "animate-pulse bg-muted rounded-md",
        className
      )}
    />
  );
}

export function ShimmerCard({ children, className }: { children?: React.ReactNode; className?: string }) {
  return (
    <div className={cn("space-y-3", className)}>
      {children}
    </div>
  );
}

export function ShimmerText({ className }: ShimmerProps) {
  return <Shimmer className={cn("h-4 w-full", className)} />;
}

export function ShimmerLine({ className }: ShimmerProps) {
  return <Shimmer className={cn("h-3 w-3/4", className)} />;
}

export function ShimmerBadge({ className }: ShimmerProps) {
  return <Shimmer className={cn("h-6 w-16", className)} />;
}

export function ShimmerButton({ className }: ShimmerProps) {
  return <Shimmer className={cn("h-9 w-20", className)} />;
}
