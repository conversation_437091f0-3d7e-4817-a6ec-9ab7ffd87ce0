import type { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  const { providerKey } = req.query as {
    providerKey?: string;
  };

  if (!providerKey) {
    return res.status(400).json({ success: false, error: 'providerKey is required' });
  }

  try {
    // Get basic connections from database only (no Nango enrichment)
    const { data: dbConnections, error: dbError } = await supabase
      .from('connections')
      .select('id, providerKey, userId, displayName, metadata')
      .eq('providerKey', providerKey);

    if (dbError) {
      throw new Error(`Database error: ${dbError.message}`);
    }

    if (!dbConnections || dbConnections.length === 0) {
      return res.status(200).json({
        success: true,
        connections: [],
        message: `No connections found for provider: ${providerKey}`
      });
    }

    // Return basic connection info without Nango enrichment
    const basicConnections = dbConnections.map(conn => ({
      id: conn.id,
      providerKey: conn.providerKey,
      userId: conn.userId,
      displayName: conn.displayName,
      metadata: conn.metadata,
    }));

    res.status(200).json({
      success: true,
      connections: basicConnections
    });
  } catch (error: any) {
    console.error('Error fetching basic connections:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal error'
    });
  }
}
