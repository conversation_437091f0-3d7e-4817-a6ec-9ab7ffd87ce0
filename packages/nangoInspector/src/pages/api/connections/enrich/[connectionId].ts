import type { NextApiRequest, NextApiResponse } from 'next';
import { Nango } from '@nangohq/node';
import { supabase } from '../../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  const { connectionId } = req.query as {
    connectionId?: string;
  };

  if (!connectionId) {
    return res.status(400).json({ success: false, error: 'connectionId is required' });
  }

  if (!process.env.NANGO_SECRET_KEY) {
    return res.status(500).json({ success: false, error: 'NANGO_SECRET_KEY not set' });
  }

  const nango = new Nango({ secretKey: process.env.NANGO_SECRET_KEY });

  try {
    // Get the connection from database to get the providerKey
    const { data: dbConnection, error: dbError } = await supabase
      .from('connections')
      .select('id, providerKey, userId, displayName, metadata')
      .eq('id', connectionId)
      .single();

    if (dbError) {
      throw new Error(`Database error: ${dbError.message}`);
    }

    if (!dbConnection) {
      return res.status(404).json({
        success: false,
        error: 'Connection not found'
      });
    }

    const enrichmentData: {
      nangoConnection?: any;
      syncStatus?: any;
      nangoMetadata?: any;
    } = {};

    try {
      // Get Nango connection details
      const nangoConnection = await nango.getConnection(dbConnection.providerKey, connectionId);
      enrichmentData.nangoConnection = nangoConnection;
    } catch (nangoError: any) {
      console.warn(`Could not fetch Nango connection for ${connectionId}:`, nangoError.message);
    }

    try {
      // Get sync status for this connection
      const syncStatus = await nango.syncStatus(dbConnection.providerKey, '*', connectionId);
      enrichmentData.syncStatus = syncStatus;
    } catch (syncError: any) {
      console.warn(`Could not fetch sync status for ${connectionId}:`, syncError.message);
    }

    try {
      // Get metadata from Nango
      const nangoMetadata = await nango.getMetadata(dbConnection.providerKey, connectionId);
      if (nangoMetadata) {
        enrichmentData.nangoMetadata = nangoMetadata;
      }
    } catch (metadataError: any) {
      console.warn(`Could not fetch metadata for ${connectionId}:`, metadataError.message);
    }

    res.status(200).json({
      success: true,
      enrichment: enrichmentData
    });
  } catch (error: any) {
    console.error('Error enriching connection:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal error'
    });
  }
}
