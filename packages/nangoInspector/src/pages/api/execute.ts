import type { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../lib/supabase';
import { getRunner } from '../../../../ma-next/netlify/functions/_tools/actions';
import { getPseudoNangoAction } from '../../../../ma-next/netlify/functions/_nango/getPseudoNangoAction';
import { ACTION_OUTPUTS } from '../../lib/actionData';
import fs from 'fs';
import path from 'path';

async function getConnectionId(providerKey: string): Promise<string | null> {
  const { data } = await supabase
    .from('connections')
    .select('id')
    .eq('providerKey', providerKey)
    .limit(1)
    .maybeSingle();
  return data?.id || null;
}

async function saveExecutionResult(
  provider: string,
  action: string,
  parameters: Record<string, any>,
  result: any,
  success: boolean,
  error?: string
): Promise<void> {
  try {
    const actionsDir = path.join(process.cwd(), 'result-data', 'actions', provider);
    
    // Ensure directory exists
    if (!fs.existsSync(actionsDir)) {
      fs.mkdirSync(actionsDir, { recursive: true });
    }
    
    // Simple filename without timestamp, matching executeProviderActions
    const filename = `${action}.json`;
    const filePath = path.join(actionsDir, filename);
    
    // Prepare data to save - matching executeProviderActions format
    const executionData = {
      input: parameters,
      output: success ? result : null,
      error: success ? null : error,
      success
    };
    
    // Save to file
    fs.writeFileSync(filePath, JSON.stringify(executionData, null, 2));
    console.log(`Execution result saved to: ${filePath}`);
  } catch (saveError) {
    console.error('Failed to save execution result:', saveError);
    // Don't throw - we don't want to fail the API call if saving fails
  }
}

interface ExecuteRequest {
  provider: string;
  action: string;
  parameters: Record<string, any>;
}

interface ExecuteResponse {
  success: boolean;
  result?: any;
  error?: string;
  valid?: boolean;
  model?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ExecuteResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  let provider: string = '';
  let action: string = '';
  let parameters: Record<string, any> = {};

  try {
    const requestData: ExecuteRequest = req.body;
    provider = requestData.provider;
    action = requestData.action;
    parameters = requestData.parameters || {};

    if (!provider || !action) {
      return res.status(400).json({
        success: false,
        error: 'Provider and action are required'
      });
    }

    // Get connection ID for the provider
    const connectionId = await getConnectionId(provider);
    if (!connectionId) {
      await saveExecutionResult(
        provider,
        action,
        parameters,
        null,
        false,
        `No ${provider} connection found`
      );
      return res.status(400).json({
        success: false,
        error: `No ${provider} connection found`
      });
    }

    // Get the action runner
    const runner = getRunner(provider, action);
    if (!runner) {
      await saveExecutionResult(
        provider,
        action,
        parameters,
        null,
        false,
        `No handler found for ${provider}:${action}`
      );
      return res.status(404).json({
        success: false,
        error: `No handler found for ${provider}:${action}`
      });
    }

    // Create pseudo Nango action instance
    const nango = getPseudoNangoAction(provider, connectionId);

    // Execute the action
    const result = await runner(nango, parameters);

    // Find the expected model for validation
    const actionDef = ACTION_OUTPUTS.find(
      (a) => a.provider === provider && a.action === action
    );

    // For now, we'll assume the result is valid
    // In a real implementation, you'd validate against the Zod schema
    const valid = true;

    // Save execution result to result-data directory
    await saveExecutionResult(provider, action, parameters, result, true);

    res.status(200).json({
      success: true,
      result,
      valid,
      model: actionDef?.model || undefined
    });

  } catch (error: any) {
    console.error('Action execution error:', error);

    // Save failed execution result
    await saveExecutionResult(
      provider,
      action,
      parameters,
      null,
      false,
      error.message || 'Internal server error'
    );

    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
}
