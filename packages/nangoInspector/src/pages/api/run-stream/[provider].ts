import type { NextApiRequest, NextApiResponse } from 'next';
import { runSlackTest } from '../../../tests/slack';
import { runGoogleDriveTest } from '../../../tests/google-drive';
import { runDropboxTest } from '../../../tests/dropbox';
import { runGithubTest } from '../../../tests/github';
import { runGoogleCalendarTest } from '../../../tests/google-calendar';
import { runGoogleDocsTest } from '../../../tests/google-docs';
import { runGoogleMailTest } from '../../../tests/google-mail';
import { runGoogleSheetTest } from '../../../tests/google-sheet';
import { runHarvestTest } from '../../../tests/harvest';
import { runLinearTest } from '../../../tests/linear';
import { runLinkedinTest } from '../../../tests/linkedin';
import { runNotionTest } from '../../../tests/notion';
import { runXSocialTest } from '../../../tests/x-social';
import { saveScriptResult } from '../../../tests/providerRunner';

const runners: Record<string, (onProgress?: (result: any) => void) => Promise<any[]>> = {
  slack: runSlackTest,
  'google-drive': runGoogleDriveTest,
  dropbox: runDropboxTest,
  github: runGithubTest,
  'google-calendar': runGoogleCalendarTest,
  'google-docs': runGoogleDocsTest,
  'google-mail': runGoogleMailTest,
  'google-sheet': runGoogleSheetTest,
  harvest: runHarvestTest,
  linear: runLinearTest,
  linkedin: runLinkedinTest,
  notion: runNotionTest,
  'x-social': runXSocialTest,
  'twitter-v2': runXSocialTest,
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') return res.status(405).end();
  const { provider } = req.query;
  const runner = typeof provider === 'string' ? runners[provider] : undefined;
  if (!runner) return res.status(404).json({ error: 'unknown provider' });

  // Set up Server-Sent Events
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  });

  const sendEvent = (type: string, data: any) => {
    res.write(`data: ${JSON.stringify({ type, ...data })}\n\n`);
  };

  try {
    sendEvent('start', {});

    // Real streaming implementation using onProgress callback
    const results = await runner((result) => {
      sendEvent('progress', {
        step: {
          action: result.action,
          input: result.input,
          output: result.output,
          valid: result.valid,
          error: result.error,
          status: result.error ? 'error' : result.output ? 'completed' : 'running',
        }
      });
    });

    await saveScriptResult(provider as string, results);
    sendEvent('complete', { results });
  } catch (err: any) {
    sendEvent('error', { error: err.message });
  } finally {
    res.end();
  }
}
