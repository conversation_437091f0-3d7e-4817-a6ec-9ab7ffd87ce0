import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs/promises';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { params } = req.query;
  
  if (!Array.isArray(params) || params.length !== 2) {
    return res.status(400).json({ error: 'Invalid parameters' });
  }

  const [provider, action] = params;
  
  try {
    // First try to load from actions directory
    let filePath = path.join(process.cwd(), 'result-data', 'actions', provider, `${action}.json`);
    
    try {
      const fileContent = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(fileContent);
      return res.status(200).json(data);
    } catch (error) {
      // Fallback to scripts directory
      filePath = path.join(process.cwd(), 'result-data', 'scripts', provider, `${action}.json`);
      
      try {
        const fileContent = await fs.readFile(filePath, 'utf-8');
        const data = JSON.parse(fileContent);
        return res.status(200).json(data);
      } catch (fallbackError) {
        // Neither location has the file
        return res.status(404).json({ error: 'Sample data not found' });
      }
    }
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
}
