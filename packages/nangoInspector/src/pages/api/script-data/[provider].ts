import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs/promises';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { provider } = req.query as { provider?: string };
  if (!provider) {
    return res.status(400).json({ error: 'Missing provider' });
  }

  try {
    const filePath = path.join(process.cwd(), 'result-data', 'scripts', `${provider}.json`);
    const content = await fs.readFile(filePath, 'utf-8');
    const data = JSON.parse(content);
    return res.status(200).json(data);
  } catch (err) {
    return res.status(404).json({ error: 'Script data not found' });
  }
}
