import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs/promises';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { params } = req.query as { params?: string[] };
  if (!Array.isArray(params) || params.length !== 2) {
    return res.status(400).json({ error: 'Invalid parameters' });
  }

  const [provider, sync] = params;

  try {
    const filePath = path.join(process.cwd(), 'result-data', 'syncs', provider, `${sync}.json`);
    const content = await fs.readFile(filePath, 'utf-8');
    const data = JSON.parse(content);

    // Return both records and raw API response for the new Raw toggle feature
    return res.status(200).json({
      records: data.output?.records || data.output,
      rawApiResponse: data.output,
      input: data.input,
      success: data.success,
      error: data.error
    });
  } catch (err) {
    return res.status(404).json({ error: 'Sync data not found' });
  }
}
