import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs/promises';
import path from 'path';
import { ACTION_OUTPUTS_KEYED } from '../../../../ma-next/src/config/nangoConstants';
import { RichResultDisplay } from '../../../../ma-next/src/components/rich-ui/RichResultDisplay';

interface ComponentData {
  provider: string;
  action: string;
  modelName: string;
  sampleData: any;
  source: 'actions' | 'scripts';
}

interface GroupedData {
  [provider: string]: {
    [modelName: string]: ComponentData[];
  };
}

// Helper function to get action sort priority
function getActionSortPriority(actionName: string): number {
  return 0;
}

// Helper function to get model sort priority
function getModelSortPriority(modelName: string): number {
  const lowerName = modelName.toLowerCase();
  return 0
}

async function findResultFiles(): Promise<ComponentData[]> {
  const components: ComponentData[] = [];
  const resultDataDir = path.join(process.cwd(), 'result-data');

  try {
    // Check if result-data directory exists
    await fs.access(resultDataDir);
  } catch {
    // result-data directory doesn't exist, return empty arraygi
    return components;
  }

  // Helper function to scan a directory for result files
  async function scanDirectory(dirPath: string, source: 'actions' | 'scripts') {
    try {
      const providers = await fs.readdir(dirPath);

      for (const provider of providers) {
        const providerPath = path.join(dirPath, provider);
        const stat = await fs.lstat(providerPath);

        if (stat.isDirectory()) {
          try {
            const files = await fs.readdir(providerPath);

            for (const file of files) {
              if (file.endsWith('.json')) {
                const action = file.replace('.json', '');
                const actionKey = `${provider}:${action}`;

                // Check if this action has a corresponding output model and rich component
                const modelName = ACTION_OUTPUTS_KEYED[actionKey as keyof typeof ACTION_OUTPUTS_KEYED];

                if (modelName && RichResultDisplay.canDisplay(provider, action)) {
                  try {
                    const filePath = path.join(providerPath, file);
                    const fileContent = await fs.readFile(filePath, 'utf-8');
                    const sampleData = JSON.parse(fileContent);

                    components.push({
                      provider,
                      action,
                      modelName,
                      sampleData,
                      source,
                    });
                  } catch (error) {
                    console.warn(`Failed to read/parse ${provider}/${file}:`, error);
                  }
                }
              }
            }
          } catch (error) {
            console.warn(`Failed to read provider directory ${provider}:`, error);
          }
        }
      }
    } catch (error) {
      console.warn(`Failed to scan directory ${dirPath}:`, error);
    }
  }

  // Scan actions directory first (preferred)
  const actionsDir = path.join(resultDataDir, 'actions');
  await scanDirectory(actionsDir, 'actions');

  // Scan scripts directory for fallback data, but only add if not already found in actions
  const scriptsDir = path.join(resultDataDir, 'scripts');
  const existingKeys = new Set(components.map(c => `${c.provider}:${c.action}`));

  const scriptComponents: ComponentData[] = [];
  await scanDirectory(scriptsDir, 'scripts');

  // Re-scan scripts directory to get the components
  try {
    const providers = await fs.readdir(scriptsDir);

    for (const provider of providers) {
      const providerPath = path.join(scriptsDir, provider);
      const stat = await fs.lstat(providerPath);

      if (stat.isDirectory()) {
        try {
          const files = await fs.readdir(providerPath);

          for (const file of files) {
            if (file.endsWith('.json')) {
              const action = file.replace('.json', '');
              const actionKey = `${provider}:${action}`;

              // Only add if not already found in actions directory
              if (!existingKeys.has(actionKey)) {
                const modelName = ACTION_OUTPUTS_KEYED[actionKey as keyof typeof ACTION_OUTPUTS_KEYED];

                if (modelName && RichResultDisplay.canDisplay(provider, action)) {
                  try {
                    const filePath = path.join(providerPath, file);
                    const fileContent = await fs.readFile(filePath, 'utf-8');
                    const sampleData = JSON.parse(fileContent);

                    scriptComponents.push({
                      provider,
                      action,
                      modelName,
                      sampleData,
                      source: 'scripts',
                    });
                  } catch (error) {
                    console.warn(`Failed to read/parse ${provider}/${file}:`, error);
                  }
                }
              }
            }
          }
        } catch (error) {
          console.warn(`Failed to read provider directory ${provider}:`, error);
        }
      }
    }
  } catch (error) {
    console.warn(`Failed to scan scripts directory:`, error);
  }

  return [...components, ...scriptComponents];
}

function groupComponentData(components: ComponentData[]): GroupedData {
  const grouped: GroupedData = {};

  // First, merge duplicates preferring actions over scripts
  const mergedComponents = new Map<string, ComponentData>();

  for (const component of components) {
    const key = `${component.provider}:${component.action}`;
    const existing = mergedComponents.get(key);

    if (!existing || (existing.source === 'scripts' && component.source === 'actions')) {
      mergedComponents.set(key, component);
    }
  }

  // Now group by provider and model
  for (const component of Array.from(mergedComponents.values())) {
    if (!grouped[component.provider]) {
      grouped[component.provider] = {};
    }

    if (!grouped[component.provider][component.modelName]) {
      grouped[component.provider][component.modelName] = [];
    }

    grouped[component.provider][component.modelName].push(component);
  }

  // Sort everything with custom heuristics
  const sortedGrouped: GroupedData = {};
  const sortedProviders = Object.keys(grouped).sort();

  for (const provider of sortedProviders) {
    sortedGrouped[provider] = {};

    // Sort models by priority then alphabetically
    const sortedModels = Object.keys(grouped[provider]).sort((a, b) => {
      const aPriority = getModelSortPriority(a);
      const bPriority = getModelSortPriority(b);
      if (aPriority !== bPriority) return aPriority - bPriority;
      return a.localeCompare(b);
    });

    for (const modelName of sortedModels) {
      // Sort actions by priority then alphabetically
      sortedGrouped[provider][modelName] = grouped[provider][modelName].sort((a, b) => {
        const aPriority = getActionSortPriority(a.action);
        const bPriority = getActionSortPriority(b.action);
        if (aPriority !== bPriority) return aPriority - bPriority;
        return a.action.localeCompare(b.action);
      });
    }
  }

  return sortedGrouped;
}

async function getUnavailableComponents(): Promise<{ provider: string; models: string[] }[]> {
  const unavailable: { provider: string; models: string[] }[] = [];
  const availableKeys = new Set<string>();

  // Get all available component keys
  const components = await findResultFiles();
  components.forEach(c => availableKeys.add(`${c.provider}:${c.action}`));

  // Find all possible provider:action combinations that have rich components but no data
  const allActionOutputs = Object.keys(ACTION_OUTPUTS_KEYED);
  const unavailableActions: { [provider: string]: Set<string> } = {};

  for (const actionKey of allActionOutputs) {
    if (!availableKeys.has(actionKey)) {
      const [provider, action] = actionKey.split(':');
      const modelName = ACTION_OUTPUTS_KEYED[actionKey as keyof typeof ACTION_OUTPUTS_KEYED];

      if (modelName && RichResultDisplay.canDisplay(provider, action)) {
        if (!unavailableActions[provider]) {
          unavailableActions[provider] = new Set();
        }
        unavailableActions[provider].add(modelName);
      }
    }
  }

  // Convert to array format
  for (const [provider, models] of Object.entries(unavailableActions)) {
    unavailable.push({
      provider,
      models: Array.from(models).sort(),
    });
  }

  return unavailable.sort((a, b) => a.provider.localeCompare(b.provider));
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const components = await findResultFiles();
    const grouped = groupComponentData(components);
    const unavailable = await getUnavailableComponents();

    res.status(200).json({
      success: true,
      components,
      grouped,
      unavailable,
      stats: {
        totalComponents: components.length,
        totalProviders: Object.keys(grouped).length,
        totalModels: Object.values(grouped).reduce(
          (total, providerData) => total + Object.keys(providerData).length,
          0
        ),
        unavailableProviders: unavailable.length,
        unavailableModels: unavailable.reduce((total, p) => total + p.models.length, 0),
      }
    });
  } catch (error: any) {
    console.error('Error in ui-storyboard-data API:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
}
