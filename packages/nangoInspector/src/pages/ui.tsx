import { useEffect, useState, useRef } from 'react';
import { Activity, Eye, AlertCircle, BarChart3, ArrowLeft } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import { ThemeToggle } from '@/components/ThemeToggle';
import { ThemeProvider } from '@/components/ThemeProvider';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { RichResultDisplay } from '../../../ma-next/src/components/rich-ui/RichResultDisplay';
import {
  ACTION_OUTPUTS_KEYED,
  SYNC_OUTPUTS_KEYED,
} from '../../../ma-next/src/config/nangoConstants';
import {
  DropboxIcon,
  GithubIcon,
  GmailIcon,
  GoogleCalendarIcon,
  GoogleDocsIcon,
  GoogleDriveIcon,
  GoogleSheetsIcon,
  HarvestIcon,
  LinearIcon,
  LinkedInIcon,
  NotionIcon,
  SlackIcon,
  TwitterIcon,
} from '../../../ma-next/src/components/icons/providers';

// Icon mapping for providers
const PROVIDER_ICONS = {
  slack: SlackIcon,
  github: GithubIcon,
  'google-calendar': GoogleCalendarIcon,
  'google-mail': GmailIcon,
  'google-docs': GoogleDocsIcon,
  'google-drive': GoogleDriveIcon,
  'google-sheet': GoogleSheetsIcon,
  dropbox: DropboxIcon,
  notion: NotionIcon,
  linear: LinearIcon,
  linkedin: LinkedInIcon,
  'twitter-v2': TwitterIcon,
  harvest: HarvestIcon,
} as const;

interface ComponentData {
  provider: string;
  action: string;
  modelName: string;
  sampleData: any;
  source: 'actions' | 'scripts';
}

interface GroupedData {
  [provider: string]: {
    [modelName: string]: ComponentData[];
  };
}

interface UnavailableComponent {
  provider: string;
  models: string[];
}

// Helper function to get unique source types for a model
function getSourceTypes(modelName: string): { actions: number; syncs: number } {
  let actions = 0;
  let syncs = 0;

  // Count actions that produce this model
  Object.values(ACTION_OUTPUTS_KEYED).forEach(model => {
    if (model === modelName) actions++;
  });

  // Count syncs that produce this model
  Object.values(SYNC_OUTPUTS_KEYED).forEach(model => {
    if (model === modelName) syncs++;
  });

  return { actions, syncs };
}

// Helper function to get action sort priority
function getActionSortPriority(actionName: string): number {
  if (actionName.startsWith('list-')) return 1;
  if (actionName.startsWith('get-')) return 2;
  if (actionName.startsWith('fetch-')) return 3;
  if (actionName.startsWith('update-')) return 4;
  if (actionName.startsWith('delete-')) return 5;
  return 6; // anything else last
}

// Helper function to get model sort priority
function getModelSortPriority(modelName: string): number {
  const lowerName = modelName.toLowerCase();
  if (lowerName.includes('new')) return 2;
  if (lowerName.includes('create')) return 3;
  if (lowerName.includes('update')) return 4;
  if (lowerName.includes('delete')) return 5;
  return 1; // anything else first
}

function UIStoryboard() {
  const router = useRouter();
  const [componentData, setComponentData] = useState<ComponentData[]>([]);
  const [groupedData, setGroupedData] = useState<GroupedData>({});
  const [unavailableComponents, setUnavailableComponents] = useState<UnavailableComponent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUnavailable, setShowUnavailable] = useState(true);
  const [activeProvider, setActiveProvider] = useState<string | null>(null);
  const providerRefs = useRef<Record<string, HTMLDivElement | null>>({});

  useEffect(() => {
    loadAllComponentData();
  }, []);

  const loadAllComponentData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/ui-storyboard-data');
      if (!response.ok) {
        throw new Error(`Failed to load component data: ${response.statusText}`);
      }
      const data = await response.json();

      if (data.success) {
        setComponentData(data.components);
        setGroupedData(data.grouped);
        setUnavailableComponents(data.unavailable || []);
      } else {
        setError(data.error || 'Failed to load component data');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        const visible = entries.filter(e => e.isIntersecting);
        if (visible.length > 0) {
          visible.sort((a, b) => b.intersectionRatio - a.intersectionRatio);
          const provider = visible[0].target.getAttribute('data-provider');
          if (provider) setActiveProvider(provider);
        }
      },
      { rootMargin: '-40% 0px -60% 0px' }
    );

    Object.values(providerRefs.current).forEach(el => {
      if (el) observer.observe(el);
    });

    return () => observer.disconnect();
  }, [groupedData]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
          <p className="text-muted-foreground">Loading component storyboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4 max-w-md">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
          <h2 className="text-xl font-semibold">Error Loading Storyboard</h2>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  const totalComponents = componentData.length;
  const totalProviders = Object.keys(groupedData).length;
  const totalModels = Object.values(groupedData).reduce(
    (total, providerData) => total + Object.keys(providerData).length,
    0
  );
  const unavailableModels = unavailableComponents.reduce((total, p) => total + p.models.length, 0);

  return (
    <div className="min-h-screen bg-background">
      {/* Floating Provider Navigation */}
      <nav className="hidden md:block fixed top-4 left-4 z-50 max-w-48 bg-card/95 backdrop-blur-sm shadow-lg border rounded-lg p-4">
        <div className="text-xs font-semibold text-muted-foreground mb-3 uppercase tracking-wide">
          Providers
        </div>
        <div className="space-y-1 max-h-96 overflow-y-auto p-[2px]">
          {Object.keys(groupedData).map(provider => {
            const IconComponent = PROVIDER_ICONS[provider as keyof typeof PROVIDER_ICONS];
            return (
              <button
                key={provider}
                onClick={() => {
                  providerRefs.current[provider]?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                  });
                }}
                className={cn(
                  'w-full flex items-center space-x-2 px-4 py-2 text-sm rounded-md hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200',
                  activeProvider === provider && 'bg-primary text-primary-foreground shadow-sm'
                )}
              >
                {IconComponent && <IconComponent className="h-4 w-4 flex-shrink-0" />}
                <span className="capitalize truncate">{provider.replace('-', ' ')}</span>
              </button>
            );
          })}
          {/* Missing Providers Section */}
          <button
            onClick={() => {
              const missingSection = document.querySelector('[data-section="missing-providers"]');
              missingSection?.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
              });
            }}
            className="w-full flex items-center space-x-2 px-3 py-2 text-sm rounded-md hover:bg-muted/50 transition-all duration-200 text-muted-foreground pt-3 mt-3 whitespace-nowrap"
          >
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span>Missing Providers</span>
          </button>
        </div>
      </nav>
      <div className="container mx-auto p-6 space-y-8">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Inspector</span>
            </button>
            <ThemeToggle />
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Eye className="h-8 w-8" />
            <span>UI Component Storyboard</span>
          </h1>
          <p className="text-muted-foreground">
            Rich UI components for all available Nango action outputs
          </p>
          <p className="text-xs text-muted-foreground">
            {totalComponents} components across {totalModels} models from {totalProviders} providers
          </p>
        </header>

        {/* Storyboard Content */}
        <div className="space-y-8">
          {Object.entries(groupedData).map(([provider, modelGroups]) => {
            const IconComponent = PROVIDER_ICONS[provider as keyof typeof PROVIDER_ICONS];

            return (
              <div
                key={provider}
                ref={el => {
                  providerRefs.current[provider] = el;
                }}
                data-provider={provider}
                id={`provider-${provider}`}
                className="space-y-6"
              >
                {/* Provider Header */}
                <div className="flex items-center space-x-3 pb-4 border-b">
                  <div className="flex items-center space-x-2">
                    {IconComponent && <IconComponent className="h-6 w-6" />}
                    <h2 className="text-2xl font-bold capitalize">{provider.replace('-', ' ')}</h2>
                  </div>
                  <Badge variant="secondary">{Object.keys(modelGroups).length} models</Badge>
                </div>

                {/* Model Groups */}
                <div className="space-y-8">
                  {/* Models with multiple components - show in expanded layout */}
                  {Object.entries(modelGroups)
                    .filter(([, components]) => components.length > 1)
                    .sort(([a], [b]) => {
                      const aPriority = getModelSortPriority(a);
                      const bPriority = getModelSortPriority(b);
                      if (aPriority !== bPriority) return aPriority - bPriority;
                      return a.localeCompare(b);
                    })
                    .map(([modelName, components]) => {
                      const sourceTypes = getSourceTypes(modelName);
                      const sourceParts = [];
                      if (sourceTypes.actions > 0)
                        sourceParts.push(
                          `${sourceTypes.actions} action${sourceTypes.actions > 1 ? 's' : ''}`
                        );
                      if (sourceTypes.syncs > 0)
                        sourceParts.push(
                          `${sourceTypes.syncs} sync${sourceTypes.syncs > 1 ? 's' : ''}`
                        );

                      // Sort components by action priority
                      const sortedComponents = [...components].sort((a, b) => {
                        const aPriority = getActionSortPriority(a.action);
                        const bPriority = getActionSortPriority(b.action);
                        if (aPriority !== bPriority) return aPriority - bPriority;
                        return a.action.localeCompare(b.action);
                      });

                      return (
                        <div key={`${provider}-${modelName}`} className="space-y-4">
                          {/* Model Header */}
                          <div className="flex items-center space-x-3">
                            <h3 className="text-xl font-semibold text-primary">{modelName}</h3>
                            <Badge variant="outline" className="text-xs font-normal">
                              {sourceParts.join(' • ')}
                            </Badge>
                          </div>

                          {/* Component Display Row */}
                          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                            {sortedComponents.map(component => (
                              <div
                                key={`${component.provider}-${component.action}`}
                                className="space-y-3"
                              >
                                {/* Action Header */}
                                <div className="flex items-center justify-between">
                                  <h4 className="text-base font-medium">{component.action}</h4>
                                  <Badge
                                    variant={
                                      component.source === 'actions' ? 'default' : 'secondary'
                                    }
                                    className="text-xs font-normal"
                                  >
                                    {component.source}
                                  </Badge>
                                </div>

                                {/* Rich Component Display */}
                                {RichResultDisplay.canDisplay(
                                  component.provider,
                                  component.action
                                ) ? (
                                  <RichResultDisplay
                                    result={component.sampleData.output}
                                    providerKey={component.provider}
                                    actionKey={component.action}
                                    actionParameters={component.sampleData.input}
                                    context={component.action}
                                  />
                                ) : (
                                  <div className="p-4 border-2 border-dashed border-muted-foreground/20 rounded-lg text-center">
                                    <div className="text-muted-foreground text-sm">
                                      No rich UI component available
                                    </div>
                                    <div className="text-xs text-muted-foreground mt-1">
                                      Raw data: {JSON.stringify(component.sampleData.output).length}{' '}
                                      chars
                                    </div>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}

                  {/* Models with single components - show in condensed row layout */}
                  {Object.entries(modelGroups).filter(([, components]) => components.length === 1)
                    .length > 0 && (
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                      {Object.entries(modelGroups)
                        .filter(([, components]) => components.length === 1)
                        .sort(([a], [b]) => {
                          const aPriority = getModelSortPriority(a);
                          const bPriority = getModelSortPriority(b);
                          if (aPriority !== bPriority) return aPriority - bPriority;
                          return a.localeCompare(b);
                        })
                        .map(([modelName, components]) => {
                          const component = components[0];
                          const sourceTypes = getSourceTypes(modelName);
                          const sourceParts = [];
                          if (sourceTypes.actions > 0)
                            sourceParts.push(
                              `${sourceTypes.actions} action${sourceTypes.actions > 1 ? 's' : ''}`
                            );
                          if (sourceTypes.syncs > 0)
                            sourceParts.push(
                              `${sourceTypes.syncs} sync${sourceTypes.syncs > 1 ? 's' : ''}`
                            );

                          return (
                            <div key={`${provider}-${modelName}`} className="space-y-4">
                              {/* Model Header - same styling as multi-component models */}
                              <div className="flex items-center space-x-3">
                                <h3 className="text-xl font-semibold text-primary">{modelName}</h3>
                                <Badge variant="outline" className="text-xs font-normal">
                                  {sourceParts.join(' • ')}
                                </Badge>
                              </div>

                              {/* Action Header */}
                              <div className="flex items-center justify-between">
                                <h4 className="text-base font-medium">{component.action}</h4>
                                <Badge
                                  variant={component.source === 'actions' ? 'default' : 'secondary'}
                                  className="text-xs font-normal"
                                >
                                  {component.source}
                                </Badge>
                              </div>

                              {/* Rich Component Display */}
                              {RichResultDisplay.canDisplay(
                                component.provider,
                                component.action
                              ) ? (
                                <RichResultDisplay
                                  result={component.sampleData.output}
                                  providerKey={component.provider}
                                  actionKey={component.action}
                                  actionParameters={component.sampleData.input}
                                  context={component.action}
                                />
                              ) : (
                                <div className="p-4 border-2 border-dashed border-muted-foreground/20 rounded-lg text-center">
                                  <div className="text-muted-foreground text-sm">
                                    No rich UI component available
                                  </div>
                                  <div className="text-xs text-muted-foreground mt-1">
                                    Raw data: {JSON.stringify(component.sampleData.output).length}{' '}
                                    chars
                                  </div>
                                </div>
                              )}
                            </div>
                          );
                        })}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        <div className="space-y-6 pt-20" data-section="missing-providers">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-muted-foreground">Missing Sample Data</h2>
          </div>

          {showUnavailable && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {unavailableComponents.map(({ provider, models }) => {
                const IconComponent = PROVIDER_ICONS[provider as keyof typeof PROVIDER_ICONS];
                return (
                  <div
                    key={provider}
                    className="p-4 border border-dashed border-muted-foreground/30 rounded-lg"
                  >
                    <div className="flex items-center space-x-2 mb-3">
                      {IconComponent && <IconComponent className="h-5 w-5 opacity-50" />}
                      <h4 className="text-base text-muted-foreground capitalize font-medium">
                        {provider.replace('-', ' ')}
                      </h4>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      No sample data for {models.length} model{models.length > 1 ? 's' : ''}:
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {models.map(model => (
                        <Badge
                          key={model}
                          variant="outline"
                          className="text-xs opacity-60 font-normal"
                        >
                          {model}
                        </Badge>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <footer className="text-center text-xs text-muted-foreground pt-8 border-t">
          <p>Component storyboard shows all available rich UI components with their sample data.</p>
          <p className="mt-1">
            Data sources: <strong>actions</strong> (preferred) and <strong>scripts</strong>{' '}
            (fallback)
          </p>
        </footer>
      </div>
    </div>
  );
}

export default function UIPage() {
  return (
    <ThemeProvider>
      <UIStoryboard />
    </ThemeProvider>
  );
}
