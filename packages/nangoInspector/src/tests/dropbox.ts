import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runDropboxTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('dropbox', [
    { actionKey: 'list-files', params: { path: '' } },
  ], onProgress);

  const more = await executeProviderActions('dropbox', [
    { actionKey: 'create-folder', params: { path: '/ActionsInspectorTest' } },
    { actionKey: 'upload-file', params: { path: '/ActionsInspectorTest/test.txt', content: 'Hello from ActionsInspector!' } },
  ], onProgress);
  results.push(...more);

  const folderId = (more[0].output as any)?.id;
  if (folderId) {
    const final = await executeProviderActions('dropbox', [
      { actionKey: 'list-files', params: { path: '/ActionsInspectorTest' } },
      { actionKey: 'copy-file', params: { fromPath: '/ActionsInspectorTest/test.txt', toPath: '/ActionsInspectorTest/test_copy.txt' } },
      { actionKey: 'move-file', params: { fromPath: '/ActionsInspectorTest/test_copy.txt', toPath: '/ActionsInspectorTest/test_moved.txt' } },
      { actionKey: 'get-file', params: { path: '/ActionsInspectorTest/test_moved.txt' } },
      { actionKey: 'fetch-file', params: { path: '/ActionsInspectorTest/test_moved.txt' } },
      { actionKey: 'search-files', params: { query: 'ActionsInspector' } },
      { actionKey: 'delete-file', params: { path: '/ActionsInspectorTest/test_moved.txt' } },
      { actionKey: 'delete-file', params: { path: '/ActionsInspectorTest/test.txt' } },
      { actionKey: 'delete-file', params: { path: '/ActionsInspectorTest' } },
    ], onProgress);
    results.push(...final);
  }
  await saveScriptResult('dropbox', results);
  return results;
}
