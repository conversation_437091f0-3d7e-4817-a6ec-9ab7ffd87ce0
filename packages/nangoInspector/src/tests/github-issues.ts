import { executeProviderActions } from './providerRunner';

export async function runGithubIssuesTest(repo: any, onProgress?: (result: any) => void) {
  const results = await executeProviderActions('github', [
    { actionKey: 'list-issues', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name } },
  ], onProgress);

  const issue = (results[0].output as any)?.issues?.[0];
  if (issue) {
    const more = await executeProviderActions('github', [
      { actionKey: 'get-issue', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, issue_number: issue.number } },
      { actionKey: 'update-issue', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, issue_number: issue.number, title: issue.title } },
    ], onProgress);
    results.push(...more);
  } else {
    const created = await executeProviderActions('github', [
      { actionKey: 'create-issue', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, title: 'Inspector Test Issue' } },
    ], onProgress);
    results.push(...created);
  }

  return results;
}
