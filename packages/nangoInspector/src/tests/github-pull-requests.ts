import { executeProviderActions } from './providerRunner';

export async function runGithubPullRequestsTest(repo: any, onProgress?: (result: any) => void) {
  const results = await executeProviderActions('github', [
    { actionKey: 'list-pull-requests', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name } },
  ], onProgress);

  const pr = (results[0].output as any)?.pullRequests?.[0];
  if (pr) {
    const more = await executeProviderActions('github', [
      { actionKey: 'get-pull-request', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, pull_number: pr.number } },
      { actionKey: 'get-pull-request-comments', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, pull_number: pr.number } },
      { actionKey: 'get-pull-request-files', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, pull_number: pr.number } },
      { actionKey: 'get-pull-request-status', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, pull_number: pr.number } },
      { actionKey: 'update-pull-request', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, pull_number: pr.number, title: pr.title } },
      { actionKey: 'update-pull-request-branch', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, pull_number: pr.number } },
    ], onProgress);
    results.push(...more);
  }

  return results;
}
