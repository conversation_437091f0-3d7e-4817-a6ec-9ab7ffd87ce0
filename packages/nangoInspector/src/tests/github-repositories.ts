import { executeProviderActions } from './providerRunner';

export async function runGithubRepositoriesTest(repo: any, onProgress?: (result: any) => void) {
  const results = await executeProviderActions('github', [
    { actionKey: 'get-repository', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name } },
    { actionKey: 'list-branches', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name } },
    { actionKey: 'update-repository', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, description: repo.description } },
    { actionKey: 'write-file', params: { owner: repo.owner?.login || repo.owner?.name, repo: repo.name, path: 'inspector.txt', message: 'Test write', content: 'Test' } },
  ], onProgress);
  return results;
}
