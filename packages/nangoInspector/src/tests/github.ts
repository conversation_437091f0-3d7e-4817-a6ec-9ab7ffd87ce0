import { executeProviderActions, saveScriptResult } from './providerRunner';
import { runGithubIssuesTest } from './github-issues';
import { runGithubPullRequestsTest } from './github-pull-requests';
import { runGithubRepositoriesTest } from './github-repositories';

export async function runGithubTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('github', [
    { actionKey: 'list-repositories', params: {} },
  ], onProgress);

  const repos = (results[0].output as any)?.repositories;
  const testRepo = repos?.[0];
  
  if (testRepo) {
    const issues = await runGithubIssuesTest(testRepo, onProgress);
    results.push(...issues);
    const prs = await runGithubPullRequestsTest(testRepo, onProgress);
    results.push(...prs);
    const repoActions = await runGithubRepositoriesTest(testRepo, onProgress);
    results.push(...repoActions);
  }
  await saveScriptResult('github', results);
  return results;
}
