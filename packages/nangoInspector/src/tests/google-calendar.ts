import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runGoogleCalendarTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions(
    'google-calendar',
    [{ actionKey: 'list-calendars', params: {} }],
    onProgress
  );

  const calendars = (results[0].output as any)?.calendars;
  const primaryCalendar = calendars?.find((cal: any) => cal.primary) || calendars?.[0];

  if (primaryCalendar) {
    // Test list-events action
    const listEventsResult = await executeProviderActions(
      'google-calendar',
      [{ actionKey: 'list-events', params: { calendarId: primaryCalendar.id } }],
      onProgress
    );
    results.push(...listEventsResult);

    // Test create-event action
    const eventTime = new Date();
    eventTime.setHours(eventTime.getHours() + 1);
    const endTime = new Date(eventTime);
    endTime.setHours(endTime.getHours() + 1);

    const createEventResult = await executeProviderActions(
      'google-calendar',
      [
        {
          actionKey: 'create-event',
          params: {
            calendarId: primaryCalendar.id,
            summary: 'ActionsInspector Test Event',
            description: 'Created by ActionsInspector test',
            start: eventTime.toISOString(),
            end: endTime.toISOString(),
          },
        },
      ],
      onProgress
    );
    results.push(...createEventResult);

    // Get the created event ID for update and delete tests
    const createdEvent = createEventResult[0]?.output;
    const eventId = createdEvent?.id;

    if (eventId) {
      // Test update-event action
      const updateEventResult = await executeProviderActions(
        'google-calendar',
        [
          {
            actionKey: 'update-event',
            params: {
              calendarId: primaryCalendar.id,
              eventId: eventId,
              summary: 'ActionsInspector Test Event (Updated)',
              description: 'Updated by ActionsInspector test',
              start: eventTime.toISOString(),
              end: endTime.toISOString(),
            },
          },
        ],
        onProgress
      );
      results.push(...updateEventResult);

      // Test delete-event action
      const deleteEventResult = await executeProviderActions(
        'google-calendar',
        [
          {
            actionKey: 'delete-event',
            params: {
              calendarId: primaryCalendar.id,
              eventId: eventId,
            },
          },
        ],
        onProgress
      );
      results.push(...deleteEventResult);
    }
  }
  await saveScriptResult('google-calendar', results);
  return results;
}
