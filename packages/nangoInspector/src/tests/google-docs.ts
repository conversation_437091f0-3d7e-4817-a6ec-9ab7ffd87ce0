import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runGoogleDocsTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('google-docs', [
    { actionKey: 'create-document', params: { title: 'ActionsInspector Test Document' } },
  ], onProgress);

  const documentId = (results[0].output as any)?.documentId;
  
  if (documentId) {
    const more = await executeProviderActions('google-docs', [
      { actionKey: 'get-document', params: { documentId } },
      { 
        actionKey: 'update-document', 
        params: { 
          documentId,
          requests: [
            {
              insertText: {
                location: { index: 1 },
                text: 'This is a test document created by ActionsInspector.\n\nThis demonstrates Google Docs integration.'
              }
            }
          ]
        } 
      },
    ], onProgress);
    results.push(...more);

    const final = await executeProviderActions('google-docs', [
      { actionKey: 'fetch-document', params: { documentId } },
    ], onProgress);
    results.push(...final);
  }
  await saveScriptResult('google-docs', results);
  return results;
}
