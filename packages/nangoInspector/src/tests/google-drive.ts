import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runGoogleDriveTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('google-drive', [
    { actionKey: 'list-root-folders', params: {} },
  ], onProgress);

  const folderId = (results[0].output as any)?.folders?.[0]?.id;

  const more = await executeProviderActions('google-drive', [
    { actionKey: 'list-documents', params: folderId ? { folderId } : {} },
    { actionKey: 'folder-content', params: folderId ? { folderId } : { folderId: '' } },
  ], onProgress);

  results.push(...more);

  const firstDoc = (more[0].output as any)?.documents?.[0];
  if (firstDoc?.id) {
    const extras = await executeProviderActions('google-drive', [
      { actionKey: 'fetch-document', params: { id: firstDoc.id } },
      { actionKey: 'fetch-google-doc', params: { id: firstDoc.id } },
      { actionKey: 'fetch-google-sheet', params: { id: firstDoc.id } },
    ], onProgress);
    results.push(...extras);
  }

  const upload = await executeProviderActions('google-drive', [
    { actionKey: 'upload-document', params: { name: 'actions-inspector.txt', content: 'Hello', mimeType: 'text/plain' } },
  ], onProgress);
  results.push(...upload);

  await saveScriptResult('google-drive', results);
  return results;
}
