import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runGoogleMailTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('google-mail', [
    { actionKey: 'list-messages', params: { maxResults: 10 } },
  ], onProgress);

  const messages = (results[0].output as any)?.messages;
  if (messages && messages.length > 0) {
    const firstMessage = messages[0];

    const more = await executeProviderActions('google-mail', [
      { actionKey: 'get-message', params: { id: firstMessage.id } },
      { actionKey: 'modify-message-labels', params: { id: firstMessage.id, addLabelIds: [], removeLabelIds: [] } },
      { actionKey: 'trash-message', params: { id: firstMessage.id } },
      { actionKey: 'untrash-message', params: { id: firstMessage.id } },
      { actionKey: 'delete-message', params: { id: firstMessage.id } },
    ], onProgress);
    results.push(...more);

    const final = await executeProviderActions('google-mail', [
      {
        actionKey: 'compose-draft',
        params: {
          to: '<EMAIL>',
          subject: 'ActionsInspector Test Draft',
          body: 'This is a test draft created by ActionsInspector'
        }
      },
      {
        actionKey: 'compose-draft-reply',
        params: {
          sender: '<EMAIL>',
          subject: 'Re: ActionsInspector Test',
          body: 'Reply body',
          threadId: firstMessage.threadId,
          messageId: firstMessage.id,
          inReplyTo: firstMessage.id,
          references: firstMessage.id,
          date: new Date().toISOString(),
          replyBody: 'Replying via inspector'
        }
      },
      {
        actionKey: 'send-email',
        params: {
          from: '<EMAIL>',
          to: '<EMAIL>',
          headers: {},
          subject: 'ActionsInspector Email',
          body: 'This is a test email from ActionsInspector'
        }
      },
    ], onProgress);
    results.push(...final);
  }
  await saveScriptResult('google-mail', results);
  return results;
}
