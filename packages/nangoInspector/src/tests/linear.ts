import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runLinearTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('linear', [
    { actionKey: 'list-teams', params: {} },
  ], onProgress);

  const teams = (results[0].output as any)?.teams;
  const firstTeam = teams?.[0];

  if (firstTeam) {
    const more = await executeProviderActions('linear', [
      { actionKey: 'get-team', params: { id: firstTeam.id } },
      { actionKey: 'list-issues', params: { teamId: firstTeam.id } },
      { actionKey: 'list-projects', params: {} },
      { actionKey: 'fetch-models', params: {} },
    ], onProgress);
    results.push(...more);

    const final = await executeProviderActions('linear', [
      {
        actionKey: 'create-issue',
        params: {
          teamId: firstTeam.id,
          title: 'ActionsInspector Test Issue',
          description: 'This is a test issue created by ActionsInspector'
        }
      },
      { actionKey: 'create-project', params: { teamId: firstTeam.id, name: 'Inspector Project' } },
    ], onProgress);
    results.push(...final);

    const issueId = (final[0].output as any)?.id;
    if (issueId) {
      const updateIssue = await executeProviderActions('linear', [
        {
          actionKey: 'update-issue',
          params: {
            id: issueId,
            title: 'ActionsInspector Test Issue (Updated)'
          }
        },
        { actionKey: 'get-issue', params: { id: issueId } },
        { actionKey: 'delete-issue', params: { id: issueId } },
      ], onProgress);
      results.push(...updateIssue);
    }
  }

  const extras = await executeProviderActions('linear', [
    { actionKey: 'get-project', params: { id: (firstTeam?.id || '') } },
    { actionKey: 'update-project', params: { id: (firstTeam?.id || ''), name: 'Updated Project' } },
  ], onProgress);
  results.push(...extras);
  await saveScriptResult('linear', results);
  return results;
}
