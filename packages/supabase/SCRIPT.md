1. Create Supabase Account
2. Fill out local environment variables.
3. supabase link to the new account. The project reference is the url in the dashboard url. `supabase link --project-ref <project-ref>`
4. Under Connect to your project go to app frameworks and find a url to set in .env file like so: DATABASE_URL="postgresql://postgres.httutluxuftvjelfxzot:[DB_PASSWORD]@aws-0-ap-southeast-2.pooler.supabase.com:5432/postgres"
5. Run `npx prisma db push` (uses DATABASE_URL)
6. `npm run supabase:functions:deploy`
7. Add env vars to the functions vault as required by functions.
8. Copy across e-mail templates.
9. Go to https://supabase.com/dashboard/project/[project ref]/auth/providers and enable:

- Allow manual linking
- Allow anonymous sign-ins

10. Change the Authentication->URL config->Site URL to http://localhost:5173/

11. Enable pg_cron extension
