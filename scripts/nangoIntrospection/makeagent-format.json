{"actionInputs": [{"provider": "harvest", "action": "add-historical-time-entry", "description": "Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking.", "model": "HarvestAddHistoricalTimeEntryInput"}, {"provider": "harvest", "action": "create-client", "description": "Creates a new client in Harvest.", "model": "HarvestCreateClientInput"}, {"provider": "harvest", "action": "create-project", "description": "Creates a new project in Harvest.", "model": "HarvestCreateProjectInput"}, {"provider": "harvest", "action": "delete-project", "description": "Deletes a project in Harvest.", "model": "HarvestProjectInput"}, {"provider": "harvest", "action": "delete-time-entry", "description": "Deletes a time entry in Harvest.", "model": "HarvestTimeEntryInput"}, {"provider": "harvest", "action": "get-client", "description": "Gets a specific client by ID.", "model": "HarvestClientInput"}, {"provider": "harvest", "action": "get-project", "description": "Gets a specific project by ID.", "model": "HarvestProjectInput"}, {"provider": "harvest", "action": "get-time-entry", "description": "Gets a specific time entry by ID.", "model": "HarvestTimeEntryInput"}, {"provider": "harvest", "action": "list-clients", "description": "Lists clients from Harvest."}, {"provider": "harvest", "action": "list-projects", "description": "Lists projects from Harvest.", "model": "HarvestProjectsInput"}, {"provider": "harvest", "action": "list-project-tasks", "description": "Lists task assignments for a specific project in Harvest.", "model": "HarvestProjectTasksInput"}, {"provider": "harvest", "action": "list-tasks", "description": "Lists tasks from Harvest.", "model": "HarvestTasksInput"}, {"provider": "harvest", "action": "list-time-entries", "description": "Lists time entries from Harvest.", "model": "HarvestTimeEntriesInput"}, {"provider": "harvest", "action": "restart-timer", "description": "Restarts a stopped time entry in Harvest.", "model": "HarvestTimeEntryInput"}, {"provider": "harvest", "action": "start-timer", "description": "Starts a new timer for a task. Checks company settings for duration vs timestamp tracking.", "model": "HarvestStartTimerInput"}, {"provider": "harvest", "action": "stop-timer", "description": "Stops a running time entry in Harvest.", "model": "HarvestTimeEntryInput"}, {"provider": "harvest", "action": "update-time-entry", "description": "Updates an existing time entry in Harvest.", "model": "HarvestUpdateTimeEntryInput"}, {"provider": "github", "action": "add-pull-request-review-comment", "description": "Add a review comment to a pull request.", "model": "GithubAddPullRequestReviewCommentInput"}, {"provider": "github", "action": "create-issue", "description": "Creates a new issue in a repository.", "model": "GithubCreateIssueInput"}, {"provider": "github", "action": "create-organization-repository", "description": "Creates a new repository within a specified organization.", "model": "GithubCreateOrganizationRepositoryInput"}, {"provider": "github", "action": "create-pull-request", "description": "Create a new pull request in a GitHub repository.", "model": "GithubCreatePullRequestInput"}, {"provider": "github", "action": "create-pull-request-review", "description": "Submit a review on a pull request.", "model": "GithubCreatePullRequestReviewInput"}, {"provider": "github", "action": "create-repository", "description": "Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url.", "model": "GithubCreateRepositoryInput"}, {"provider": "github", "action": "delete-repository", "description": "Deletes a repository.", "model": "GithubRepositoryInput"}, {"provider": "github", "action": "get-issue", "description": "Gets a specific issue by number.", "model": "GithubIssueInput"}, {"provider": "github", "action": "get-pull-request", "description": "Get details of a specific pull request in a GitHub repository.", "model": "GithubPullRequestInput"}, {"provider": "github", "action": "get-pull-request-comments", "description": "Get the review comments on a pull request.", "model": "GithubPullRequestInput"}, {"provider": "github", "action": "get-pull-request-files", "description": "Get the files changed in a specific pull request.", "model": "GithubPullRequestInput"}, {"provider": "github", "action": "get-pull-request-status", "description": "Get the combined status of all status checks for a pull request.", "model": "GithubPullRequestInput"}, {"provider": "github", "action": "get-repository", "description": "Gets a specific repository by owner and name.", "model": "GithubRepositoryInput"}, {"provider": "github", "action": "list-branches", "description": "List branches in a repository.", "model": "GithubListBranchesInput"}, {"provider": "github", "action": "list-issues", "description": "Lists issues for a repository.", "model": "GithubIssuesInput"}, {"provider": "github", "action": "list-pull-requests", "description": "List pull requests in a GitHub repository.", "model": "GithubListPullRequestsInput"}, {"provider": "github", "action": "list-repositories", "description": "Lists repositories for the authenticated user."}, {"provider": "github", "action": "merge-pull-request", "description": "Merge a pull request in a GitHub repository.", "model": "GithubMergePullRequestInput"}, {"provider": "github", "action": "update-issue", "description": "Updates an existing issue.", "model": "GithubUpdateIssueInput"}, {"provider": "github", "action": "update-pull-request", "description": "Update an existing pull request in a GitHub repository.", "model": "GithubUpdatePullRequestInput"}, {"provider": "github", "action": "update-pull-request-branch", "description": "Update the branch of a pull request with the latest changes from the base branch.", "model": "GithubUpdatePullRequestBranchInput"}, {"provider": "github", "action": "update-repository", "description": "Updates an existing repository.", "model": "GithubUpdateRepositoryInput"}, {"provider": "github", "action": "write-file", "description": "Write content to a particular github file within a repo. If", "model": "GithubWriteFileInput"}, {"provider": "slack", "action": "add-reaction-as-user", "description": "Adds an emoji reaction to a message as the authenticated user.", "model": "SlackAddReactionInput"}, {"provider": "slack", "action": "delete-message-as-user", "description": "Deletes a message from a channel as the authenticated user.", "model": "SlackDeleteMessageInput"}, {"provider": "slack", "action": "get-channel-history", "description": "Retrieves message history for a specific channel.", "model": "SlackGetChannelHistoryInput"}, {"provider": "slack", "action": "get-message-permalink", "description": "Retrieves a permalink for a specific message.", "model": "SlackGetPermalinkInput"}, {"provider": "slack", "action": "get-user-info", "description": "Retrieves information about a specific user.", "model": "SlackGetUserInfoInput"}, {"provider": "slack", "action": "list-channels", "description": "Lists channels in Slack.", "model": "SlackListChannelsInput"}, {"provider": "slack", "action": "search-messages", "description": "Searches for messages matching a query.", "model": "SlackSearchMessagesInput"}, {"provider": "slack", "action": "send-message-as-user", "description": "Sends a message to a Slack channel as the authenticated user.", "model": "SlackSendMessageInput"}, {"provider": "slack", "action": "update-message-as-user", "description": "Updates an existing message in a channel as the authenticated user.", "model": "SlackUpdateMessageInput"}, {"provider": "google-calendar", "action": "create-event", "description": "Creates a new event in Google Calendar. Can either be full-day or time-based.", "model": "GoogleCalendarEventInput"}, {"provider": "google-calendar", "action": "delete-event", "description": "Deletes an event from Google Calendar.", "model": "GoogleCalendarEventDeleteInput"}, {"provider": "google-calendar", "action": "list-calendars", "description": "Lists all calendars available to the authenticated user."}, {"provider": "google-calendar", "action": "list-events", "description": "Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past.", "model": "GoogleCalendarEventsInput"}, {"provider": "google-calendar", "action": "update-event", "description": "Updates an event in Google Calendar.", "model": "GoogleCalendarEventUpdateInput"}, {"provider": "google-mail", "action": "compose-draft", "description": "Creates a new draft email in Gmail.", "model": "GmailDraftInput"}, {"provider": "google-mail", "action": "compose-draft-reply", "description": "Creates a new draft email that is a reply to an existing email.", "model": "GmailReplyDraftInput"}, {"provider": "google-mail", "action": "delete-message", "description": "Permanently deletes the specified message. Bypasses Trash.", "model": "GmailMessageIdInput"}, {"provider": "google-mail", "action": "get-message", "description": "Retrieves a specific email by <PERSON>.", "model": "GmailGetMessageInput"}, {"provider": "google-mail", "action": "list-messages", "description": "Lists emails from Gmail inbox with optional filtering.", "model": "GmailListMessagesInput"}, {"provider": "google-mail", "action": "modify-message-labels", "description": "Modifies the labels applied to a specific message.", "model": "GmailModifyMessageLabelsInput"}, {"provider": "google-mail", "action": "send-email", "description": "Sends an email via Gmail.", "model": "GmailSendEmailInput"}, {"provider": "google-mail", "action": "trash-message", "description": "Moves the specified message to the trash.", "model": "GmailMessageIdInput"}, {"provider": "google-mail", "action": "untrash-message", "description": "Removes the specified message from the trash.", "model": "GmailMessageIdInput"}, {"provider": "dropbox", "action": "copy-file", "description": "Copy a file or folder to a different location in Dropbox and return metadata of the copied item", "model": "DropboxCopyInput"}, {"provider": "dropbox", "action": "create-folder", "description": "Create a new folder in Dropbox and return folder metadata", "model": "DropboxCreateFolderInput"}, {"provider": "dropbox", "action": "delete-file", "description": "Delete a file or folder in Dropbox and return metadata of the deleted item", "model": "DropboxDeleteInput"}, {"provider": "dropbox", "action": "get-file", "description": "Get file metadata and a download URL (not the actual file content)", "model": "DropboxGetFileInput"}, {"provider": "dropbox", "action": "list-files", "description": "List files and folders in a Dropbox directory", "model": "DropboxListFilesInput"}, {"provider": "dropbox", "action": "move-file", "description": "Move a file or folder to a different location in Dropbox and return metadata of the moved item", "model": "DropboxMoveInput"}, {"provider": "dropbox", "action": "search-files", "description": "Search for files and folders in Dropbox by filename or content", "model": "DropboxSearchInput"}, {"provider": "dropbox", "action": "upload-file", "description": "Upload text or binary content as a file to Dropbox", "model": "DropboxUploadFileInput"}, {"provider": "notion", "action": "create-database", "description": "Creates a new Notion database as a subpage of a specified page.", "model": "NotionCreateDatabaseInput"}, {"provider": "notion", "action": "create-page", "description": "Creates a new Notion page.", "model": "NotionCreatePageInput"}, {"provider": "notion", "action": "get-database", "description": "Retrieves a specific Notion Database object by its ID.", "model": "NotionGetDatabaseInput"}, {"provider": "notion", "action": "get-page", "description": "Retrieves a specific Notion Page object by its ID.", "model": "NotionGetPageInput"}, {"provider": "notion", "action": "query-database", "description": "Queries a Notion database for pages, with optional filters and sorts.", "model": "NotionQueryDatabaseInput"}, {"provider": "notion", "action": "search", "description": "Searches pages and databases in Notion. IMPORTANT - Use \"\" to search for everything.", "model": "NotionSearchInput"}, {"provider": "notion", "action": "update-database", "description": "Updates properties of an existing Notion database. ALSO USED TO \"delete\" a database, set archive to true.", "model": "NotionUpdateDatabaseInput"}, {"provider": "notion", "action": "update-page", "description": "Updates properties of an existing Notion page. ALSO USED TO \"delete\" a page, set archive to true.", "model": "NotionUpdatePageInput"}, {"provider": "google-docs", "action": "create-document", "description": "Creates a blank Google Document.", "model": "GoogleDocsCreateDocumentInput"}, {"provider": "google-docs", "action": "get-document", "description": "Retrieves a specific Google Document.", "model": "GoogleDocsGetDocumentInput"}, {"provider": "google-docs", "action": "update-document", "description": "Applies batch updates to a Google Document.", "model": "GoogleDocsUpdateDocumentInput"}, {"provider": "linear", "action": "create-issue", "description": "Creates a new issue in Linear.", "model": "LinearCreateIssueInput"}, {"provider": "linear", "action": "create-project", "description": "Creates a new project in Linear.", "model": "LinearCreateProjectInput"}, {"provider": "linear", "action": "delete-issue", "description": "Deletes an issue in Linear.", "model": "LinearIssueInput"}, {"provider": "linear", "action": "fetch-models", "description": "Introspection endpoint to fetch the models available"}, {"provider": "linear", "action": "get-issue", "description": "Gets a specific issue by ID.", "model": "LinearIssueInput"}, {"provider": "linear", "action": "get-project", "description": "Gets a specific project by ID.", "model": "LinearProjectInput"}, {"provider": "linear", "action": "get-team", "description": "Gets a specific team by ID.", "model": "LinearTeamInput"}, {"provider": "linear", "action": "list-issues", "description": "Lists issues from Linear.", "model": "LinearIssuesInput"}, {"provider": "linear", "action": "list-projects", "description": "List all projects from Linear", "model": "LinearProjectsInput"}, {"provider": "linear", "action": "list-teams", "description": "Lists teams from Linear.", "model": "LinearTeamsInput"}, {"provider": "linear", "action": "update-issue", "description": "Updates an existing issue in Linear.", "model": "LinearUpdateIssueInput"}, {"provider": "linear", "action": "update-project", "description": "Updates an existing project in Linear.", "model": "LinearUpdateProjectInput"}, {"provider": "google-sheet", "action": "create-sheet", "description": "Creates a new Google Sheet with optional initial data.", "model": "GoogleSheetCreateInput"}, {"provider": "google-sheet", "action": "fetch-spreadsheet", "description": "Fetches the content of a spreadsheet given its ID.", "model": "SpreadsheetId"}, {"provider": "google-sheet", "action": "update-sheet", "description": "Updates an existing Google Sheet with new data.", "model": "GoogleSheetUpdateInput"}, {"provider": "google-drive", "action": "fetch-document", "description": "Fetches the content of a file given its ID, processes the data using", "model": "IdEntity"}, {"provider": "google-drive", "action": "fetch-google-doc", "description": "Fetches the content of a native google document given its ID. Outputs", "model": "IdEntity"}, {"provider": "google-drive", "action": "fetch-google-sheet", "description": "Fetches the content of a native google spreadsheet given its ID. Outputs", "model": "IdEntity"}, {"provider": "google-drive", "action": "folder-content", "description": "Fetches the top-level content (files and folders) of a folder given its ID.", "model": "FolderContentInput"}, {"provider": "google-drive", "action": "list-documents", "description": "Lists documents in Google Drive with optional filtering by folder ID and document type.", "model": "ListDocumentsInput"}, {"provider": "google-drive", "action": "list-root-folders", "description": "Lists folders at the root level of Google Drive."}, {"provider": "google-drive", "action": "upload-document", "description": "Uploads a file to Google Drive. The file is uploaded to the root directory", "model": "UploadFileInput"}, {"provider": "twitter-v2", "action": "get-user-profile", "description": "Gets the authenticated user's profile information from X."}, {"provider": "twitter-v2", "action": "send-post", "description": "Sends a new post to <PERSON>.", "model": "XSocialPostInput"}, {"provider": "linkedin", "action": "get-user-profile", "description": "Gets the authenticated user's profile information from LinkedIn."}, {"provider": "linkedin", "action": "send-post", "description": "Creates a new post on LinkedIn.", "model": "LinkedInPostInput"}], "actionInputModelsDictionary": {"HarvestAddHistoricalTimeEntryInput": {"project_id": {"type": "number", "optional": false}, "task_id": {"type": "number", "optional": false}, "spent_date": {"type": "string", "optional": false}, "hours": {"type": "number", "optional": true}, "started_time": {"type": "string", "optional": true}, "ended_time": {"type": "string", "optional": true}, "notes": {"type": "string", "optional": true}, "user_id": {"type": "number", "optional": true}, "external_reference": {"type": {"$ref": "HarvestExternalReferenceInput"}, "optional": true}}, "HarvestExternalReferenceInput": {"id": {"type": "string", "optional": true}, "group_id": {"type": "string", "optional": true}, "account_id": {"type": "string", "optional": true}, "permalink": {"type": "string", "optional": true}, "service": {"type": "string", "optional": true}, "service_icon_url": {"type": "string", "optional": true}}, "HarvestCreateClientInput": {"name": {"type": "string", "optional": false}, "is_active": {"type": "boolean", "optional": true}, "address": {"type": "union", "optional": true, "union": ["string", null]}, "currency": {"type": "union", "optional": true, "union": ["string", null]}}, "HarvestCreateProjectInput": {"client_id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "is_billable": {"type": "boolean", "optional": false}, "bill_by": {"type": "string", "optional": false}, "budget_by": {"type": "string", "optional": false}, "is_fixed_fee": {"type": "boolean", "optional": true}, "fee": {"type": "union", "optional": true, "union": ["number", null]}, "hourly_rate": {"type": "union", "optional": true, "union": ["number", null]}, "budget": {"type": "union", "optional": true, "union": ["number", null]}, "budget_is_monthly": {"type": "boolean", "optional": true}, "notify_when_over_budget": {"type": "boolean", "optional": true}, "over_budget_notification_percentage": {"type": "number", "optional": true}, "show_budget_to_all": {"type": "boolean", "optional": true}, "cost_budget": {"type": "union", "optional": true, "union": ["number", null]}, "cost_budget_include_expenses": {"type": "boolean", "optional": true}, "notes": {"type": "union", "optional": true, "union": ["string", null]}, "starts_on": {"type": "union", "optional": true, "union": ["string", null]}, "ends_on": {"type": "union", "optional": true, "union": ["string", null]}}, "HarvestProjectInput": {"project_id": {"type": "number", "optional": false}}, "HarvestTimeEntryInput": {"timeEntryId": {"type": "number", "optional": false}, "id": {"type": "number", "optional": true}}, "HarvestClientInput": {"client_id": {"type": "number", "optional": false}}, "HarvestProjectsInput": {"client_id": {"type": "number", "optional": true}, "is_active": {"type": "boolean", "optional": true}, "page": {"type": "number", "optional": true}, "per_page": {"type": "number", "optional": true}}, "HarvestProjectTasksInput": {"project_id": {"type": "number", "optional": false}}, "HarvestTasksInput": {"is_active": {"type": "boolean", "optional": true}, "updated_since": {"type": "string", "optional": true}, "page": {"type": "number", "optional": true}, "per_page": {"type": "number", "optional": true}}, "HarvestTimeEntriesInput": {"userId": {"type": "number", "optional": true}, "clientId": {"type": "number", "optional": true}, "projectId": {"type": "number", "optional": true}, "taskId": {"type": "number", "optional": true}, "from": {"type": "string", "optional": true}, "to": {"type": "string", "optional": true}, "page": {"type": "number", "optional": true}, "perPage": {"type": "number", "optional": true}}, "HarvestStartTimerInput": {"project_id": {"type": "number", "optional": false}, "task_id": {"type": "number", "optional": false}, "spent_date": {"type": "string", "optional": false}, "started_time": {"type": "string", "optional": true}, "notes": {"type": "string", "optional": true}, "user_id": {"type": "number", "optional": true}, "external_reference": {"type": {"$ref": "HarvestExternalReferenceInput"}, "optional": true}}, "HarvestUpdateTimeEntryInput": {"time_entry_id": {"type": "number", "optional": false}, "project_id": {"type": "number", "optional": true}, "task_id": {"type": "number", "optional": true}, "spent_date": {"type": "string", "optional": true}, "hours": {"type": "number", "optional": true}, "started_time": {"type": "string", "optional": true}, "ended_time": {"type": "string", "optional": true}, "notes": {"type": "string", "optional": true}, "external_reference": {"type": {"$ref": "HarvestExternalReferenceInput"}, "optional": true}}, "GithubAddPullRequestReviewCommentInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "pull_number": {"type": "number", "optional": false}, "body": {"type": "string", "optional": false}, "commit_id": {"type": "string", "optional": true}, "path": {"type": "string", "optional": true}, "subject_type": {"type": "string", "optional": true}, "line": {"type": "number", "optional": true}, "side": {"type": "string", "optional": true}, "start_line": {"type": "number", "optional": true}, "start_side": {"type": "string", "optional": true}, "in_reply_to": {"type": "number", "optional": true}, "diff_hunk": {"type": "string", "optional": true}}, "GithubCreateIssueInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "title": {"type": "string", "optional": false}, "body": {"type": "string", "optional": true}, "assignees": {"type": "string", "optional": true, "array": true}, "labels": {"type": "string", "optional": true, "array": true}}, "GithubCreateOrganizationRepositoryInput": {"org": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "description": {"type": "string", "optional": true}, "homepage": {"type": "string", "optional": true}, "private": {"type": "boolean", "optional": true}, "has_issues": {"type": "boolean", "optional": true}, "has_projects": {"type": "boolean", "optional": true}, "has_wiki": {"type": "boolean", "optional": true}}, "GithubCreatePullRequestInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "title": {"type": "string", "optional": false}, "body": {"type": "string", "optional": true}, "head": {"type": "string", "optional": false}, "base": {"type": "string", "optional": false}, "draft": {"type": "boolean", "optional": true}, "maintainer_can_modify": {"type": "boolean", "optional": true}}, "GithubCreatePullRequestReviewInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "pullNumber": {"type": "number", "optional": false}, "body": {"type": "string", "optional": true}, "event": {"type": "string", "optional": false}, "commitId": {"type": "string", "optional": true}, "comments": {"type": {"$ref": "GithubDraftReviewComment"}, "optional": true, "array": true}}, "GithubDraftReviewComment": {"path": {"type": "string", "optional": false}, "position": {"type": "number", "optional": true}, "line": {"type": "number", "optional": true}, "side": {"type": "string", "optional": true}, "start_line": {"type": "number", "optional": true}, "start_side": {"type": "string", "optional": true}, "body": {"type": "string", "optional": false}}, "GithubCreateRepositoryInput": {"name": {"type": "string", "optional": false}, "description": {"type": "string", "optional": true}, "private": {"type": "boolean", "optional": true}, "has_issues": {"type": "boolean", "optional": true}, "has_projects": {"type": "boolean", "optional": true}, "has_wiki": {"type": "boolean", "optional": true}, "auto_init": {"type": "boolean", "optional": true}, "gitignore_template": {"type": "string", "optional": true}, "license_template": {"type": "string", "optional": true}}, "GithubRepositoryInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}}, "GithubIssueInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "issue_number": {"type": "number", "optional": false}}, "GithubPullRequestInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "pullNumber": {"type": "number", "optional": false}}, "GithubListBranchesInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "protected": {"type": "boolean", "optional": true}, "per_page": {"type": "number", "optional": true}, "page": {"type": "number", "optional": true}}, "GithubIssuesInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "state": {"type": "string", "optional": true}, "sort": {"type": "string", "optional": true}, "direction": {"type": "string", "optional": true}, "per_page": {"type": "number", "optional": true}, "page": {"type": "number", "optional": true}}, "GithubListPullRequestsInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "state": {"type": "string", "optional": true}, "head": {"type": "string", "optional": true}, "base": {"type": "string", "optional": true}, "sort": {"type": "string", "optional": true}, "direction": {"type": "string", "optional": true}, "per_page": {"type": "number", "optional": true}, "page": {"type": "number", "optional": true}}, "GithubMergePullRequestInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "pullNumber": {"type": "number", "optional": false}, "commit_title": {"type": "string", "optional": true}, "commit_message": {"type": "string", "optional": true}, "merge_method": {"type": "string", "optional": true}}, "GithubUpdateIssueInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "issue_number": {"type": "number", "optional": false}, "title": {"type": "string", "optional": true}, "body": {"type": "string", "optional": true}, "state": {"type": "string", "optional": true}, "assignees": {"type": "string", "optional": true, "array": true}, "labels": {"type": "string", "optional": true, "array": true}}, "GithubUpdatePullRequestInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "pullNumber": {"type": "number", "optional": false}, "title": {"type": "string", "optional": true}, "body": {"type": "string", "optional": true}, "state": {"type": "string", "optional": true}, "base": {"type": "string", "optional": true}, "maintainer_can_modify": {"type": "boolean", "optional": true}}, "GithubUpdatePullRequestBranchInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "pullNumber": {"type": "number", "optional": false}, "expectedHeadSha": {"type": "string", "optional": true}}, "GithubUpdateRepositoryInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "name": {"type": "string", "optional": true}, "description": {"type": "string", "optional": true}, "private": {"type": "boolean", "optional": true}, "has_issues": {"type": "boolean", "optional": true}, "has_projects": {"type": "boolean", "optional": true}, "has_wiki": {"type": "boolean", "optional": true}, "default_branch": {"type": "string", "optional": true}}, "GithubWriteFileInput": {"owner": {"type": "string", "optional": false}, "repo": {"type": "string", "optional": false}, "path": {"type": "string", "optional": false}, "message": {"type": "string", "optional": false}, "content": {"type": "string", "optional": false}, "sha": {"type": "union", "optional": false, "union": ["string", "undefined"]}}, "SlackAddReactionInput": {"name": {"type": "string", "optional": false}, "channel": {"type": "string", "optional": false}, "timestamp": {"type": "string", "optional": false}}, "SlackDeleteMessageInput": {"channel": {"type": "string", "optional": false}, "ts": {"type": "string", "optional": false}}, "SlackGetChannelHistoryInput": {"channel": {"type": "string", "optional": false}, "limit": {"type": "number", "optional": true}, "latest": {"type": "string", "optional": true}, "oldest": {"type": "string", "optional": true}, "cursor": {"type": "string", "optional": true}}, "SlackGetPermalinkInput": {"channel": {"type": "string", "optional": false}, "message_ts": {"type": "string", "optional": false}}, "SlackGetUserInfoInput": {"user": {"type": "string", "optional": false}}, "SlackListChannelsInput": {"types": {"type": "string", "optional": true}, "limit": {"type": "number", "optional": true}, "cursor": {"type": "string", "optional": true}}, "SlackSearchMessagesInput": {"query": {"type": "string", "optional": false}, "sort": {"type": "string", "optional": true}, "sort_dir": {"type": "string", "optional": true}, "count": {"type": "number", "optional": true}, "page": {"type": "number", "optional": true}}, "SlackSendMessageInput": {"channel": {"type": "string", "optional": false}, "text": {"type": "string", "optional": false}, "thread_ts": {"type": "string", "optional": true}}, "SlackUpdateMessageInput": {"channel": {"type": "string", "optional": false}, "ts": {"type": "string", "optional": false}, "text": {"type": "string", "optional": false}}, "GoogleCalendarEventInput": {"summary": {"type": "string", "optional": false, "description": "Event title / name."}, "description": {"type": "string", "optional": true, "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."}, "location": {"type": "string", "optional": true, "description": "Free form text."}, "start": {"type": "string", "optional": false, "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."}, "end": {"type": "string", "optional": false, "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."}, "timeZone": {"type": "string", "optional": true, "description": "An IANA Time Zone e.g. (Area/City)"}, "attendees": {"type": "string", "optional": true, "description": "A list of attendee email addresses.", "array": true}}, "GoogleCalendarEventDeleteInput": {"calendarId": {"type": "string", "optional": false, "description": "Calendar identifier. Use \"primary\" unless otherwise advised."}, "eventId": {"type": "string", "optional": false, "description": "Event identifier."}, "sendUpdates": {"type": "string", "optional": true, "description": "Whether to send notifications about the deletion of the event."}}, "GoogleCalendarEventsInput": {"calendarId": {"type": "string", "optional": false, "description": "Calendar identifier. Use \"primary\" unless otherwise advised."}, "timeMin": {"type": "string", "optional": true, "description": "Lower bound (inclusive) for an event's end time to filter by. Defaults to now. ISO8601 string format."}, "timeMax": {"type": "string", "optional": true, "description": "Upper bound (exclusive) for an event's start time to filter by. Defaults to unbounded. ISO8601 string format."}, "maxResults": {"type": "number", "optional": true, "description": "Defaults to 250. Max 2500."}, "pageToken": {"type": "string", "optional": true, "description": "Token as per a previous response to get another page of results."}, "orderBy": {"type": "string", "optional": true}, "q": {"type": "string", "optional": true, "description": "Free text search terms to find events that match these terms."}, "singleEvents": {"type": "boolean", "optional": true}, "timeZone": {"type": "string", "optional": true, "description": "Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."}}, "GoogleCalendarEventUpdateInput": {"calendarId": {"type": "string", "optional": false, "description": "Calendar identifier. Use \"primary\" unless otherwise advised."}, "eventId": {"type": "string", "optional": false, "description": "Event identifier."}, "sendUpdates": {"type": "string", "optional": true, "description": "Whether to send notifications about the event update."}, "summary": {"type": "string", "optional": true, "description": "Event title / name."}, "description": {"type": "string", "optional": true, "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."}, "location": {"type": "string", "optional": true, "description": "Free form text."}, "start": {"type": "string", "optional": true, "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."}, "end": {"type": "string", "optional": true, "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."}, "timeZone": {"type": "string", "optional": true, "description": "An IANA Time Zone e.g. (Area/City)"}, "attendees": {"type": {"$ref": "GoogleCalendarAttendeeInput"}, "optional": true, "description": "A list of attendee email addresses.", "array": true}}, "GoogleCalendarAttendeeInput": {"id": {"type": "string", "optional": true}, "email": {"type": "string", "optional": false}, "displayName": {"type": "string", "optional": true}, "responseStatus": {"type": "string", "optional": false}, "optional": {"type": "boolean", "optional": true}, "resource": {"type": "boolean", "optional": true}, "comment": {"type": "string", "optional": true}}, "GmailDraftInput": {"recipient": {"type": "string", "optional": false}, "subject": {"type": "string", "optional": false}, "body": {"type": "string", "optional": true}, "headers": {"type": "object", "optional": true}, "attachments": {"type": {"$ref": "UrlAccessibleFile"}, "optional": true, "array": true}}, "UrlAccessibleFile": {"url": {"type": "string", "optional": false}, "authentication": {"type": {"$ref": "UrlAuthentication"}, "optional": false}}, "UrlAuthentication": {"providerKey": {"type": "string", "optional": false}, "connectionId": {"type": "string", "optional": false}}, "GmailReplyDraftInput": {"sender": {"type": "string", "optional": false}, "subject": {"type": "string", "optional": false}, "body": {"type": "string", "optional": false}, "threadId": {"type": "string", "optional": false}, "messageId": {"type": "string", "optional": false}, "inReplyTo": {"type": "string", "optional": false}, "references": {"type": "string", "optional": false}, "date": {"type": "string", "optional": false}, "replyBody": {"type": "string", "optional": false}}, "GmailMessageIdInput": {"messageId": {"type": "string", "optional": false}}, "GmailGetMessageInput": {"id": {"type": "string", "optional": false}, "format": {"type": "string", "optional": true}}, "GmailListMessagesInput": {"maxResults": {"type": "number", "optional": true}, "labelIds": {"type": "string", "optional": true, "array": true}, "q": {"type": "string", "optional": true}, "pageToken": {"type": "string", "optional": true}}, "GmailModifyMessageLabelsInput": {"messageId": {"type": "string", "optional": false}, "addLabelIds": {"type": "string", "optional": true, "array": true}, "removeLabelIds": {"type": "string", "optional": true, "array": true}}, "GmailSendEmailInput": {"to": {"type": "string", "optional": false}, "subject": {"type": "string", "optional": false}, "body": {"type": "string", "optional": false}, "from": {"type": "string", "optional": true}, "cc": {"type": "string", "optional": true}, "bcc": {"type": "string", "optional": true}, "attachments": {"type": {"$ref": "UrlAccessibleFile"}, "optional": true, "array": true}, "GmailAttachment": {"type": "any", "optional": false}}, "DropboxCopyInput": {"from_path": {"type": "string", "optional": false}, "to_path": {"type": "string", "optional": false}, "allow_shared_folder": {"type": "boolean", "optional": true}, "autorename": {"type": "boolean", "optional": true}}, "DropboxCreateFolderInput": {"path": {"type": "string", "optional": false}, "autorename": {"type": "boolean", "optional": true}}, "DropboxDeleteInput": {"path": {"type": "string", "optional": false}}, "DropboxGetFileInput": {"path": {"type": "string", "optional": false}}, "DropboxListFilesInput": {"path": {"type": "string", "optional": false}, "recursive": {"type": "boolean", "optional": true}, "limit": {"type": "number", "optional": true}, "include_deleted": {"type": "boolean", "optional": true}}, "DropboxMoveInput": {"from_path": {"type": "string", "optional": false}, "to_path": {"type": "string", "optional": false}, "allow_shared_folder": {"type": "boolean", "optional": true}, "autorename": {"type": "boolean", "optional": true}, "allow_ownership_transfer": {"type": "boolean", "optional": true}}, "DropboxSearchInput": {"query": {"type": "string", "optional": false}, "path": {"type": "string", "optional": true}, "max_results": {"type": "number", "optional": true}, "mode": {"type": "string", "optional": true}}, "DropboxUploadFileInput": {"path": {"type": "string", "optional": false}, "content": {"type": "string", "optional": false}, "encoding": {"type": "string", "optional": true}, "mode": {"type": "string", "optional": true}, "autorename": {"type": "boolean", "optional": true}, "mute": {"type": "boolean", "optional": true}}, "NotionCreateDatabaseInput": {"parentId": {"type": "string", "optional": false}, "title": {"type": {"$ref": "NotionRichText"}, "optional": false, "array": true}, "properties": {"type": "object", "optional": false}}, "NotionRichText": {"type": {"type": "string", "optional": true}, "text": {"type": {"$ref": "NotionRichTextContent"}, "optional": true}, "annotations": {"type": {"$ref": "NotionRichTextAnnotations"}, "optional": true}, "plain_text": {"type": "string", "optional": true}, "href": {"type": "union", "optional": true, "union": ["string", null]}}, "NotionRichTextContent": {"content": {"type": "string", "optional": true}, "link": {"type": "union", "optional": true, "union": ["Record<string, any>", null]}}, "NotionRichTextAnnotations": {"bold": {"type": "boolean", "optional": true}, "italic": {"type": "boolean", "optional": true}, "strikethrough": {"type": "boolean", "optional": true}, "underline": {"type": "boolean", "optional": true}, "code": {"type": "boolean", "optional": true}, "color": {"type": "string", "optional": true}}, "NotionCreatePageInput": {"parentId": {"type": "string", "optional": false}, "parentType": {"type": "string", "optional": true}, "properties": {"type": "object", "optional": false}, "children": {"type": "object", "optional": true, "array": true}}, "NotionGetDatabaseInput": {"databaseId": {"type": "string", "optional": false}}, "NotionGetPageInput": {"pageId": {"type": "string", "optional": false}}, "NotionQueryDatabaseInput": {"databaseId": {"type": "string", "optional": false}, "filter": {"type": "object", "optional": true}, "sorts": {"type": "object", "optional": true, "array": true}, "start_cursor": {"type": "string", "optional": true}, "page_size": {"type": "number", "optional": true}}, "NotionSearchInput": {"query": {"type": "string", "optional": true}, "sort": {"type": {"$ref": "NotionSort"}, "optional": true}, "filter": {"type": {"$ref": "NotionFilter"}, "optional": true}, "start_cursor": {"type": "string", "optional": true}, "page_size": {"type": "number", "optional": true}}, "NotionSort": {"direction": {"type": "string", "optional": true}, "timestamp": {"type": "string", "optional": true}}, "NotionFilter": {"value": {"type": "string", "optional": true}, "property": {"type": "string", "optional": true}}, "NotionUpdateDatabaseInput": {"databaseId": {"type": "string", "optional": false}, "title": {"type": {"$ref": "NotionRichText"}, "optional": true, "array": true}, "description": {"type": {"$ref": "NotionRichText"}, "optional": true, "array": true}, "properties": {"type": "object", "optional": true}, "archived": {"type": "boolean", "optional": true}}, "NotionUpdatePageInput": {"pageId": {"type": "string", "optional": false}, "properties": {"type": "object", "optional": true}, "archived": {"type": "boolean", "optional": true}, "icon": {"type": "union", "optional": true, "union": ["Record<string, any>", null]}, "cover": {"type": "union", "optional": true, "union": ["Record<string, any>", null]}}, "GoogleDocsCreateDocumentInput": {"title": {"type": "string", "optional": true}}, "GoogleDocsGetDocumentInput": {"documentId": {"type": "string", "optional": false}}, "GoogleDocsUpdateDocumentInput": {"documentId": {"type": "string", "optional": false}, "requests": {"type": "object", "optional": false, "array": true}, "writeControl": {"type": "object", "optional": true}}, "LinearCreateIssueInput": {"teamId": {"type": "string", "optional": false}, "title": {"type": "string", "optional": false}, "description": {"type": "string", "optional": true}, "stateId": {"type": "string", "optional": true}, "assigneeId": {"type": "string", "optional": true}, "priority": {"type": "number", "optional": true}, "projectId": {"type": "string", "optional": true}, "labelIds": {"type": "string", "optional": true, "array": true}}, "LinearCreateProjectInput": {"name": {"type": "string", "optional": false}, "description": {"type": "string", "optional": true}, "icon": {"type": "string", "optional": true}, "color": {"type": "string", "optional": true}, "teamIds": {"type": "string", "optional": false, "array": true}}, "LinearIssueInput": {"issueId": {"type": "string", "optional": false}}, "LinearProjectInput": {"projectId": {"type": "string", "optional": false}}, "LinearTeamInput": {"teamId": {"type": "string", "optional": false}}, "LinearIssuesInput": {"teamId": {"type": "string", "optional": true}, "projectId": {"type": "string", "optional": true}, "states": {"type": "string", "optional": true, "array": true}, "assigneeId": {"type": "string", "optional": true}, "priority": {"type": "number", "optional": true}, "sortBy": {"type": "string", "optional": true}, "sortOrder": {"type": "string", "optional": true}, "limit": {"type": "number", "optional": true}, "first": {"type": "number", "optional": true}, "after": {"type": "string", "optional": true}}, "LinearProjectsInput": {"first": {"type": "number", "optional": true}, "after": {"type": "string", "optional": true}}, "LinearTeamsInput": {"first": {"type": "number", "optional": true}, "after": {"type": "string", "optional": true}}, "LinearUpdateIssueInput": {"issueId": {"type": "string", "optional": false}, "title": {"type": "string", "optional": true}, "description": {"type": "string", "optional": true}, "stateId": {"type": "string", "optional": true}, "assigneeId": {"type": "string", "optional": true}, "priority": {"type": "number", "optional": true}, "projectId": {"type": "string", "optional": true}, "labelIds": {"type": "string", "optional": true, "array": true}}, "LinearUpdateProjectInput": {"projectId": {"type": "string", "optional": false}, "name": {"type": "string", "optional": true}, "description": {"type": "string", "optional": true}, "icon": {"type": "string", "optional": true}, "color": {"type": "string", "optional": true}, "state": {"type": "string", "optional": true}, "teamIds": {"type": "string", "optional": true, "array": true}}, "GoogleSheetCreateInput": {"title": {"type": "string", "optional": false}, "sheets": {"type": {"$ref": "GoogleSheetTab"}, "optional": true, "array": true}}, "GoogleSheetTab": {"title": {"type": "string", "optional": false}, "data": {"type": {"$ref": "SheetData"}, "optional": true}}, "SheetData": {"rows": {"type": {"$ref": "SheetRow"}, "optional": false, "array": true}}, "SheetRow": {"cells": {"type": "string", "optional": false, "array": true}}, "SpreadsheetId": {"id": {"type": "string", "optional": false}}, "GoogleSheetUpdateInput": {"spreadsheetId": {"type": "string", "optional": false}, "updates": {"type": {"$ref": "SheetUpdate"}, "optional": false, "array": true}}, "SheetUpdate": {"sheetId": {"type": "number", "optional": true}, "sheetName": {"type": "string", "optional": true}, "range": {"type": "string", "optional": true}, "startRow": {"type": "number", "optional": true}, "startColumn": {"type": "number", "optional": true}, "data": {"type": {"$ref": "SheetData"}, "optional": false}}, "IdEntity": {"id": {"type": "string", "optional": false}}, "FolderContentInput": {"id": {"type": "string", "optional": true}, "cursor": {"type": "string", "optional": true}}, "ListDocumentsInput": {"folderId": {"type": "string", "optional": true}, "mimeType": {"type": "string", "optional": true}, "pageSize": {"type": "number", "optional": true}, "pageToken": {"type": "string", "optional": true}, "orderBy": {"type": "string", "optional": true}}, "UploadFileInput": {"content": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "mimeType": {"type": "string", "optional": false}, "folderId": {"type": "union", "optional": true, "union": ["string", "undefined"]}, "description": {"type": "union", "optional": true, "union": ["string", "undefined"]}, "isBase64": {"type": "union", "optional": true, "union": ["boolean", "undefined"]}}, "XSocialPostInput": {"text": {"type": "string", "optional": false}, "reply_to": {"type": "string", "optional": true}, "quote": {"type": "string", "optional": true}}, "LinkedInPostInput": {"text": {"type": "string", "optional": false}, "visibility": {"type": "string", "optional": false}}}, "actionOutputs": [{"provider": "harvest", "action": "add-historical-time-entry", "model": "HarvestTimeEntry"}, {"provider": "harvest", "action": "create-client", "model": "HarvestClient"}, {"provider": "harvest", "action": "create-project", "model": "HarvestProject"}, {"provider": "harvest", "action": "delete-project", "model": "HarvestDeleteProjectOutput"}, {"provider": "harvest", "action": "delete-time-entry", "model": "HarvestDeleteTimeEntryOutput"}, {"provider": "harvest", "action": "get-client", "model": "HarvestClient"}, {"provider": "harvest", "action": "get-project", "model": "HarvestProject"}, {"provider": "harvest", "action": "get-time-entry", "model": "HarvestTimeEntry"}, {"provider": "harvest", "action": "list-clients", "model": "HarvestClientList"}, {"provider": "harvest", "action": "list-projects", "model": "HarvestProjectList"}, {"provider": "harvest", "action": "list-project-tasks", "model": "HarvestProjectTaskList"}, {"provider": "harvest", "action": "list-tasks", "model": "HarvestTaskList"}, {"provider": "harvest", "action": "list-time-entries", "model": "HarvestTimeEntryList"}, {"provider": "harvest", "action": "restart-timer", "model": "HarvestTimeEntry"}, {"provider": "harvest", "action": "start-timer", "model": "HarvestTimeEntry"}, {"provider": "harvest", "action": "stop-timer", "model": "HarvestTimeEntry"}, {"provider": "harvest", "action": "update-time-entry", "model": "HarvestTimeEntry"}, {"provider": "github", "action": "add-pull-request-review-comment", "model": "GithubPullRequestComment"}, {"provider": "github", "action": "create-issue", "model": "GithubIssue"}, {"provider": "github", "action": "create-organization-repository", "model": "GithubRepository"}, {"provider": "github", "action": "create-pull-request", "model": "GithubPullRequest"}, {"provider": "github", "action": "create-pull-request-review", "model": "GithubPullRequestReview"}, {"provider": "github", "action": "create-repository", "model": "GithubRepository"}, {"provider": "github", "action": "delete-repository", "model": "GithubDeleteRepositoryOutput"}, {"provider": "github", "action": "get-issue", "model": "GithubIssue"}, {"provider": "github", "action": "get-pull-request", "model": "GithubPullRequest"}, {"provider": "github", "action": "get-pull-request-comments", "model": "GithubPullRequestCommentList"}, {"provider": "github", "action": "get-pull-request-files", "model": "GithubPullRequestFileList"}, {"provider": "github", "action": "get-pull-request-status", "model": "GithubCombinedStatus"}, {"provider": "github", "action": "get-repository", "model": "GithubRepository"}, {"provider": "github", "action": "list-branches", "model": "GithubBranchList"}, {"provider": "github", "action": "list-issues", "model": "GithubIssueList"}, {"provider": "github", "action": "list-pull-requests", "model": "GithubPullRequestList"}, {"provider": "github", "action": "list-repositories", "model": "GithubRepositoryList"}, {"provider": "github", "action": "merge-pull-request", "model": "GithubMergeResult"}, {"provider": "github", "action": "update-issue", "model": "GithubIssue"}, {"provider": "github", "action": "update-pull-request", "model": "GithubPullRequest"}, {"provider": "github", "action": "update-pull-request-branch", "model": "GithubBranchUpdateResult"}, {"provider": "github", "action": "update-repository", "model": "GithubRepository"}, {"provider": "github", "action": "write-file", "model": "GithubWriteFileActionResult"}, {"provider": "slack", "action": "add-reaction-as-user", "model": "SlackReactionOutput"}, {"provider": "slack", "action": "delete-message-as-user", "model": "SlackDeleteMessageOutput"}, {"provider": "slack", "action": "get-channel-history", "model": "SlackMessageList"}, {"provider": "slack", "action": "get-message-permalink", "model": "SlackPermalinkOutput"}, {"provider": "slack", "action": "get-user-info", "model": "SlackUserInfo"}, {"provider": "slack", "action": "list-channels", "model": "SlackConversationsList"}, {"provider": "slack", "action": "search-messages", "model": "SlackSearchResultList"}, {"provider": "slack", "action": "send-message-as-user", "model": "SlackSendMessageOutput"}, {"provider": "slack", "action": "update-message-as-user", "model": "SlackUpdateMessageOutput"}, {"provider": "google-calendar", "action": "create-event", "model": "GoogleCalendarEvent"}, {"provider": "google-calendar", "action": "delete-event", "model": "GoogleCalendarEventDeleteOutput"}, {"provider": "google-calendar", "action": "list-calendars", "model": "GoogleCalendarList"}, {"provider": "google-calendar", "action": "list-events", "model": "GoogleCalendarEventList"}, {"provider": "google-calendar", "action": "update-event", "model": "GoogleCalendarEvent"}, {"provider": "google-mail", "action": "compose-draft", "model": "GmailDraftOutput"}, {"provider": "google-mail", "action": "compose-draft-reply", "model": "GmailReplyDraftOutput"}, {"provider": "google-mail", "action": "delete-message", "model": "GmailDeleteMessageOutput"}, {"provider": "google-mail", "action": "get-message", "model": "GmailMessage"}, {"provider": "google-mail", "action": "list-messages", "model": "GmailMessageList"}, {"provider": "google-mail", "action": "modify-message-labels", "model": "GmailMessage"}, {"provider": "google-mail", "action": "send-email", "model": "GmailSendEmailOutput"}, {"provider": "google-mail", "action": "trash-message", "model": "GmailMessage"}, {"provider": "google-mail", "action": "untrash-message", "model": "GmailMessage"}, {"provider": "dropbox", "action": "copy-file", "model": "DropboxEntry"}, {"provider": "dropbox", "action": "create-folder", "model": "DropboxFolder"}, {"provider": "dropbox", "action": "delete-file", "model": "DropboxDeleteResult"}, {"provider": "dropbox", "action": "get-file", "model": "DropboxFile"}, {"provider": "dropbox", "action": "list-files", "model": "DropboxFileList"}, {"provider": "dropbox", "action": "move-file", "model": "DropboxEntry"}, {"provider": "dropbox", "action": "search-files", "model": "DropboxSearchResult"}, {"provider": "dropbox", "action": "upload-file", "model": "DropboxFile"}, {"provider": "notion", "action": "create-database", "model": "NotionDatabase"}, {"provider": "notion", "action": "create-page", "model": "NotionPageOrDatabase"}, {"provider": "notion", "action": "get-database", "model": "NotionDatabase"}, {"provider": "notion", "action": "get-page", "model": "NotionPageOrDatabase"}, {"provider": "notion", "action": "query-database", "model": "NotionQueryDatabaseOutput"}, {"provider": "notion", "action": "search", "model": "NotionSearchOutput"}, {"provider": "notion", "action": "update-database", "model": "NotionDatabase"}, {"provider": "notion", "action": "update-page", "model": "NotionPageOrDatabase"}, {"provider": "google-docs", "action": "create-document", "model": "GoogleDocsDocument"}, {"provider": "google-docs", "action": "get-document", "model": "GoogleDocsDocument"}, {"provider": "google-docs", "action": "update-document", "model": "GoogleDocsUpdateDocumentOutput"}, {"provider": "linear", "action": "create-issue", "model": "LinearIssue"}, {"provider": "linear", "action": "create-project", "model": "LinearProject"}, {"provider": "linear", "action": "delete-issue", "model": "LinearDeleteIssueOutput"}, {"provider": "linear", "action": "fetch-models", "model": "ModelResponse"}, {"provider": "linear", "action": "get-issue", "model": "LinearIssue"}, {"provider": "linear", "action": "get-project", "model": "LinearProject"}, {"provider": "linear", "action": "get-team", "model": "LinearTeam"}, {"provider": "linear", "action": "list-issues", "model": "LinearIssueList"}, {"provider": "linear", "action": "list-projects", "model": "LinearProjectList"}, {"provider": "linear", "action": "list-teams", "model": "LinearTeamList"}, {"provider": "linear", "action": "update-issue", "model": "LinearIssue"}, {"provider": "linear", "action": "update-project", "model": "LinearProject"}, {"provider": "google-sheet", "action": "create-sheet", "model": "GoogleSheetCreateOutput"}, {"provider": "google-sheet", "action": "fetch-spreadsheet", "model": "Spreadsheet"}, {"provider": "google-sheet", "action": "update-sheet", "model": "GoogleSheetUpdateOutput"}, {"provider": "google-drive", "action": "fetch-document", "model": "Anonymous_googledrive_action_fetchdocument_output"}, {"provider": "google-drive", "action": "fetch-google-doc", "model": "JSONDocument"}, {"provider": "google-drive", "action": "fetch-google-sheet", "model": "JSONSpreadsheet"}, {"provider": "google-drive", "action": "folder-content", "model": "FolderContent"}, {"provider": "google-drive", "action": "list-documents", "model": "GoogleDriveDocumentList"}, {"provider": "google-drive", "action": "list-root-folders", "model": "GoogleDriveFolderList"}, {"provider": "google-drive", "action": "upload-document", "model": "GoogleDocument"}, {"provider": "twitter-v2", "action": "get-user-profile", "model": "XSocialUserProfile"}, {"provider": "twitter-v2", "action": "send-post", "model": "XSocialPostOutput"}, {"provider": "linkedin", "action": "get-user-profile", "model": "LinkedInUserProfile"}, {"provider": "linkedin", "action": "send-post", "model": "LinkedInPostOutput"}], "actionOutputModelsDictionary": {"HarvestTimeEntry": {"id": {"type": "number", "optional": false}, "spent_date": {"type": "string", "optional": false}, "hours": {"type": "number", "optional": false}, "notes": {"type": "string", "optional": true}, "is_locked": {"type": "boolean", "optional": false}, "is_running": {"type": "boolean", "optional": false}, "is_billed": {"type": "boolean", "optional": false}, "timer_started_at": {"type": "union", "optional": true, "union": ["string", null]}, "started_time": {"type": "union", "optional": true, "union": ["string", null]}, "ended_time": {"type": "union", "optional": true, "union": ["string", null]}, "user": {"type": {"$ref": "HarvestUser"}, "optional": false}, "client": {"type": {"$ref": "HarvestClientInTimeEntry"}, "optional": false}, "project": {"type": {"$ref": "ProjectReference"}, "optional": false}, "task": {"type": {"$ref": "HarvestTaskInTimeEntry"}, "optional": false}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "hours_without_timer": {"type": "number", "optional": true}, "rounded_hours": {"type": "number", "optional": true}, "locked_reason": {"type": "union", "optional": true, "union": ["string", null]}, "is_closed": {"type": "boolean", "optional": true}, "billable": {"type": "boolean", "optional": true}, "budgeted": {"type": "boolean", "optional": true}, "billable_rate": {"type": "union", "optional": true, "union": ["number", null]}, "cost_rate": {"type": "union", "optional": true, "union": ["number", null]}, "user_assignment": {"type": "object", "optional": true}, "task_assignment": {"type": "object", "optional": true}, "invoice": {"type": "union", "optional": true, "union": ["Record<string, any>", null]}, "external_reference": {"type": "union", "optional": true, "union": ["HarvestExternalReferenceInput", null]}}, "HarvestUser": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "email": {"type": "string", "optional": true}}, "HarvestClientInTimeEntry": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "currency": {"type": "string", "optional": true}, "is_active": {"type": "boolean", "optional": true}}, "ProjectReference": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "code": {"type": "union", "optional": true, "union": ["string", null]}, "is_active": {"type": "boolean", "optional": true}, "is_billable": {"type": "boolean", "optional": true}}, "HarvestTaskInTimeEntry": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "is_active": {"type": "boolean", "optional": true}, "billable_by_default": {"type": "boolean", "optional": true}, "is_default": {"type": "boolean", "optional": true}, "created_at": {"type": "string", "optional": true}, "updated_at": {"type": "string", "optional": true}, "default_hourly_rate": {"type": "union", "optional": true, "union": ["number", null]}}, "HarvestClient": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "is_active": {"type": "boolean", "optional": false}, "address": {"type": "union", "optional": true, "union": ["string", null]}, "statement_key": {"type": "string", "optional": true}, "created_at": {"type": "string", "optional": true}, "updated_at": {"type": "string", "optional": true}, "currency": {"type": "string", "optional": true}}, "HarvestProject": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "code": {"type": "union", "optional": true, "union": ["string", null]}, "client": {"type": {"$ref": "HarvestClientReference"}, "optional": false}, "is_active": {"type": "boolean", "optional": false}, "is_billable": {"type": "boolean", "optional": false}, "is_fixed_fee": {"type": "boolean", "optional": false}, "bill_by": {"type": "string", "optional": false}, "budget": {"type": "union", "optional": true, "union": ["number", null]}, "budget_by": {"type": "string", "optional": false}, "budget_is_monthly": {"type": "boolean", "optional": false}, "notify_when_over_budget": {"type": "boolean", "optional": false}, "over_budget_notification_percentage": {"type": "number", "optional": true}, "show_budget_to_all": {"type": "boolean", "optional": false}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "starts_on": {"type": "union", "optional": true, "union": ["string", null]}, "ends_on": {"type": "union", "optional": true, "union": ["string", null]}, "over_budget_notification_date": {"type": "union", "optional": true, "union": ["string", null]}, "notes": {"type": "union", "optional": true, "union": ["string", null]}, "cost_budget": {"type": "union", "optional": true, "union": ["number", null]}, "cost_budget_include_expenses": {"type": "boolean", "optional": false}, "hourly_rate": {"type": "union", "optional": true, "union": ["number", null]}, "fee": {"type": "union", "optional": true, "union": ["number", null]}}, "HarvestClientReference": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "currency": {"type": "string", "optional": false}}, "HarvestDeleteProjectOutput": {"success": {"type": "boolean", "optional": false}, "message": {"type": "string", "optional": false}}, "HarvestDeleteTimeEntryOutput": {"success": {"type": "boolean", "optional": false}, "message": {"type": "string", "optional": false}}, "HarvestClientList": {"clients": {"type": {"$ref": "HarvestClient"}, "optional": false, "array": true}, "per_page": {"type": "number", "optional": false}, "total_pages": {"type": "number", "optional": false}, "total_entries": {"type": "number", "optional": false}, "next_page": {"type": "union", "optional": true, "union": ["number", null]}, "previous_page": {"type": "union", "optional": true, "union": ["number", null]}, "page": {"type": "number", "optional": true}, "links": {"type": "object", "optional": true}}, "HarvestProjectList": {"projects": {"type": {"$ref": "HarvestProject"}, "optional": false, "array": true}, "per_page": {"type": "number", "optional": false}, "total_pages": {"type": "number", "optional": false}, "total_entries": {"type": "number", "optional": false}, "next_page": {"type": "union", "optional": true, "union": ["number", null]}, "previous_page": {"type": "union", "optional": true, "union": ["number", null]}, "page": {"type": "number", "optional": true}, "links": {"type": {"$ref": "HarvestPaginationLinks"}, "optional": true}}, "HarvestPaginationLinks": {"first": {"type": "string", "optional": true}, "next": {"type": "union", "optional": true, "union": ["string", null]}, "previous": {"type": "union", "optional": true, "union": ["string", null]}, "last": {"type": "string", "optional": true}}, "HarvestProjectTaskList": {"task_assignments": {"type": {"$ref": "HarvestProjectTask"}, "optional": false, "array": true}, "per_page": {"type": "number", "optional": true}, "total_pages": {"type": "number", "optional": true}, "total_entries": {"type": "number", "optional": true}, "next_page": {"type": "union", "optional": true, "union": ["number", null]}, "previous_page": {"type": "union", "optional": true, "union": ["number", null]}, "page": {"type": "number", "optional": true}, "links": {"type": {"$ref": "PaginationLinks"}, "optional": true}}, "HarvestProjectTask": {"id": {"type": "number", "optional": false}, "billable": {"type": "boolean", "optional": false}, "is_active": {"type": "boolean", "optional": false}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "hourly_rate": {"type": "union", "optional": true, "union": ["number", null]}, "budget": {"type": "union", "optional": true, "union": ["number", null]}, "project": {"type": {"$ref": "ProjectReference"}, "optional": true}, "task": {"type": {"$ref": "TaskInAssignment"}, "optional": false}}, "TaskInAssignment": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}}, "PaginationLinks": {"first": {"type": "string", "optional": true}, "next": {"type": "union", "optional": true, "union": ["string", null]}, "previous": {"type": "union", "optional": true, "union": ["string", null]}, "last": {"type": "string", "optional": true}}, "HarvestTaskList": {"tasks": {"type": {"$ref": "HarvestTask"}, "optional": false, "array": true}, "per_page": {"type": "number", "optional": false}, "total_pages": {"type": "number", "optional": false}, "total_entries": {"type": "number", "optional": false}, "next_page": {"type": "union", "optional": true, "union": ["number", null]}, "previous_page": {"type": "union", "optional": true, "union": ["number", null]}, "page": {"type": "number", "optional": false}, "links": {"type": "object", "optional": true}}, "HarvestTask": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "is_active": {"type": "boolean", "optional": false}, "billable_by_default": {"type": "boolean", "optional": false}, "is_default": {"type": "boolean", "optional": true}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "default_hourly_rate": {"type": "union", "optional": true, "union": ["number", null]}}, "HarvestTimeEntryList": {"time_entries": {"type": {"$ref": "HarvestTimeEntry"}, "optional": false, "array": true}, "per_page": {"type": "number", "optional": false}, "total_pages": {"type": "number", "optional": false}, "total_entries": {"type": "number", "optional": false}, "next_page": {"type": "union", "optional": true, "union": ["number", null]}, "previous_page": {"type": "union", "optional": true, "union": ["number", null]}, "page": {"type": "number", "optional": true}, "links": {"type": "object", "optional": true}}, "GithubPullRequestComment": {"id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}, "pull_request_review_id": {"type": "number", "optional": false}, "diff_hunk": {"type": "string", "optional": false}, "path": {"type": "string", "optional": false}, "position": {"type": "number", "optional": false}, "original_position": {"type": "number", "optional": false}, "commit_id": {"type": "string", "optional": false}, "original_commit_id": {"type": "string", "optional": false}, "user": {"type": {"$ref": "GithubIssueCreator"}, "optional": false}, "body": {"type": "string", "optional": false}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "html_url": {"type": "string", "optional": false}, "pull_request_url": {"type": "string", "optional": false}, "author_association": {"type": "string", "optional": false}, "_links": {"type": "object", "optional": false}, "reactions": {"type": {"$ref": "GithubReactions"}, "optional": false}, "start_line": {"type": "union", "optional": false, "union": ["number", null]}, "original_start_line": {"type": "union", "optional": false, "union": ["number", null]}, "start_side": {"type": "union", "optional": false, "union": ["string", null]}, "line": {"type": "number", "optional": false}, "original_line": {"type": "number", "optional": false}, "side": {"type": "string", "optional": false}, "in_reply_to_id": {"type": "number", "optional": true}, "subject_type": {"type": "string", "optional": false}}, "GithubIssueCreator": {"login": {"type": "string", "optional": false}, "id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "avatar_url": {"type": "string", "optional": false}, "gravatar_id": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}, "html_url": {"type": "string", "optional": false}, "followers_url": {"type": "string", "optional": false}, "following_url": {"type": "string", "optional": false}, "gists_url": {"type": "string", "optional": false}, "starred_url": {"type": "string", "optional": false}, "subscriptions_url": {"type": "string", "optional": false}, "organizations_url": {"type": "string", "optional": false}, "repos_url": {"type": "string", "optional": false}, "events_url": {"type": "string", "optional": false}, "received_events_url": {"type": "string", "optional": false}, "type": {"type": "string", "optional": false}, "user_view_type": {"type": "string", "optional": false}, "site_admin": {"type": "boolean", "optional": false}}, "GithubReactions": {"url": {"type": "string", "optional": false}, "total_count": {"type": "number", "optional": false}, "+1": {"type": "number", "optional": false}, "-1": {"type": "number", "optional": false}, "laugh": {"type": "number", "optional": false}, "hooray": {"type": "number", "optional": false}, "confused": {"type": "number", "optional": false}, "heart": {"type": "number", "optional": false}, "rocket": {"type": "number", "optional": false}, "eyes": {"type": "number", "optional": false}}, "GithubIssue": {"id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}, "repository_url": {"type": "string", "optional": false}, "labels_url": {"type": "string", "optional": false}, "comments_url": {"type": "string", "optional": false}, "events_url": {"type": "string", "optional": false}, "html_url": {"type": "string", "optional": false}, "number": {"type": "number", "optional": false}, "title": {"type": "string", "optional": false}, "state": {"type": "string", "optional": false}, "locked": {"type": "boolean", "optional": false}, "body": {"type": "union", "optional": false, "union": ["string", null]}, "user": {"type": "union", "optional": false, "union": ["GithubIssueCreatorLite", "GithubIssueCreator"]}, "labels": {"type": {"$ref": "GithubIssueLabel"}, "optional": false, "array": true}, "assignee": {"type": "union", "optional": false, "union": ["Record<string, any>", null]}, "assignees": {"type": "object", "optional": false, "array": true}, "milestone": {"type": "union", "optional": false, "union": ["string", null]}, "comments": {"type": "number", "optional": false}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "closed_at": {"type": "union", "optional": false, "union": ["string", null]}, "author_association": {"type": "string", "optional": false}, "active_lock_reason": {"type": "union", "optional": false, "union": ["string", null]}, "sub_issues_summary": {"type": {"$ref": "GithubSubIssuesSummary"}, "optional": false}, "closed_by": {"type": "union", "optional": false, "union": ["GithubIssueCreator", null]}, "reactions": {"type": {"$ref": "GithubReactions"}, "optional": false}, "timeline_url": {"type": "string", "optional": false}, "performed_via_github_app": {"type": "union", "optional": false, "union": ["Record<string, any>", null]}, "state_reason": {"type": "union", "optional": false, "union": ["string", null]}}, "GithubIssueLabel": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}, "color": {"type": "string", "optional": false}, "description": {"type": "string", "optional": false}}, "GithubSubIssuesSummary": {"total": {"type": "number", "optional": false}, "completed": {"type": "number", "optional": false}, "percent_completed": {"type": "number", "optional": false}}, "GithubRepository": {"id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "full_name": {"type": "string", "optional": false}, "private": {"type": "boolean", "optional": false}, "owner": {"type": {"$ref": "GithubIssueCreator"}, "optional": false}, "html_url": {"type": "string", "optional": false}, "description": {"type": "union", "optional": false, "union": ["string", null]}, "fork": {"type": "boolean", "optional": false}, "url": {"type": "string", "optional": false}, "forks_url": {"type": "string", "optional": false}, "keys_url": {"type": "string", "optional": false}, "collaborators_url": {"type": "string", "optional": false}, "teams_url": {"type": "string", "optional": false}, "hooks_url": {"type": "string", "optional": false}, "issue_events_url": {"type": "string", "optional": false}, "events_url": {"type": "string", "optional": false}, "assignees_url": {"type": "string", "optional": false}, "branches_url": {"type": "string", "optional": false}, "tags_url": {"type": "string", "optional": false}, "blobs_url": {"type": "string", "optional": false}, "git_tags_url": {"type": "string", "optional": false}, "git_refs_url": {"type": "string", "optional": false}, "trees_url": {"type": "string", "optional": false}, "statuses_url": {"type": "string", "optional": false}, "languages_url": {"type": "string", "optional": false}, "stargazers_url": {"type": "string", "optional": false}, "contributors_url": {"type": "string", "optional": false}, "subscribers_url": {"type": "string", "optional": false}, "subscription_url": {"type": "string", "optional": false}, "commits_url": {"type": "string", "optional": false}, "git_commits_url": {"type": "string", "optional": false}, "comments_url": {"type": "string", "optional": false}, "issue_comment_url": {"type": "string", "optional": false}, "contents_url": {"type": "string", "optional": false}, "compare_url": {"type": "string", "optional": false}, "merges_url": {"type": "string", "optional": false}, "archive_url": {"type": "string", "optional": false}, "downloads_url": {"type": "string", "optional": false}, "issues_url": {"type": "string", "optional": false}, "pulls_url": {"type": "string", "optional": false}, "milestones_url": {"type": "string", "optional": false}, "notifications_url": {"type": "string", "optional": false}, "labels_url": {"type": "string", "optional": false}, "releases_url": {"type": "string", "optional": false}, "deployments_url": {"type": "string", "optional": false}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "pushed_at": {"type": "string", "optional": false}, "git_url": {"type": "string", "optional": false}, "ssh_url": {"type": "string", "optional": false}, "clone_url": {"type": "string", "optional": false}, "svn_url": {"type": "string", "optional": false}, "homepage": {"type": "union", "optional": false, "union": ["string", null]}, "size": {"type": "number", "optional": false}, "stargazers_count": {"type": "number", "optional": false}, "watchers_count": {"type": "number", "optional": false}, "language": {"type": "union", "optional": false, "union": ["string", null]}, "has_issues": {"type": "boolean", "optional": false}, "has_projects": {"type": "boolean", "optional": false}, "has_downloads": {"type": "boolean", "optional": false}, "has_wiki": {"type": "boolean", "optional": false}, "has_pages": {"type": "boolean", "optional": false}, "has_discussions": {"type": "boolean", "optional": false}, "forks_count": {"type": "number", "optional": false}, "mirror_url": {"type": "union", "optional": false, "union": ["string", null]}, "archived": {"type": "boolean", "optional": false}, "disabled": {"type": "boolean", "optional": false}, "open_issues_count": {"type": "number", "optional": false}, "license": {"type": "union", "optional": false, "union": ["string", null]}, "allow_forking": {"type": "boolean", "optional": false}, "is_template": {"type": "boolean", "optional": false}, "web_commit_signoff_required": {"type": "boolean", "optional": false}, "topics": {"type": "string", "optional": false, "array": true}, "visibility": {"type": "string", "optional": false}, "forks": {"type": "number", "optional": false}, "open_issues": {"type": "number", "optional": false}, "watchers": {"type": "number", "optional": false}, "default_branch": {"type": "string", "optional": false}}, "GithubPullRequest": {"url": {"type": "string", "optional": false}, "id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "html_url": {"type": "string", "optional": false}, "diff_url": {"type": "string", "optional": false}, "patch_url": {"type": "string", "optional": false}, "issue_url": {"type": "string", "optional": false}, "number": {"type": "number", "optional": false}, "state": {"type": "string", "optional": false}, "locked": {"type": "boolean", "optional": false}, "title": {"type": "string", "optional": false}, "user": {"type": {"$ref": "GithubIssueCreator"}, "optional": false}, "body": {"type": "union", "optional": false, "union": ["string", null]}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}, "closed_at": {"type": "union", "optional": false, "union": ["string", null]}, "merged_at": {"type": "union", "optional": false, "union": ["string", null]}, "merge_commit_sha": {"type": "union", "optional": false, "union": ["string", null]}, "assignee": {"type": "union", "optional": false, "union": ["GithubIssueAssignee", null]}, "assignees": {"type": {"$ref": "GithubIssueAssignee"}, "optional": false, "array": true}, "requested_reviewers": {"type": {"$ref": "GithubIssueAssignee"}, "optional": false, "array": true}, "requested_teams": {"type": {"$ref": "GithubTeamRef"}, "optional": false, "array": true}, "labels": {"type": {"$ref": "GithubIssueLabel"}, "optional": false, "array": true}, "milestone": {"type": "union", "optional": false, "union": ["string", null]}, "draft": {"type": "boolean", "optional": false}, "commits_url": {"type": "string", "optional": false}, "review_comments_url": {"type": "string", "optional": false}, "review_comment_url": {"type": "string", "optional": false}, "comments_url": {"type": "string", "optional": false}, "statuses_url": {"type": "string", "optional": false}, "head": {"type": "object", "optional": false}, "base": {"type": "object", "optional": false}, "_links": {"type": "object", "optional": false}, "author_association": {"type": "string", "optional": false}, "auto_merge": {"type": "union", "optional": false, "union": ["Record<string, any>", null]}, "active_lock_reason": {"type": "union", "optional": false, "union": ["string", null]}, "merged": {"type": "boolean", "optional": false}, "mergeable": {"type": "union", "optional": false, "union": ["boolean", null]}, "rebaseable": {"type": "union", "optional": false, "union": ["boolean", null]}, "mergeable_state": {"type": "string", "optional": false}, "merged_by": {"type": "union", "optional": false, "union": ["GithubIssueCreator", null]}, "comments": {"type": "number", "optional": false}, "review_comments": {"type": "number", "optional": false}, "maintainer_can_modify": {"type": "boolean", "optional": false}, "commits": {"type": "number", "optional": false}, "additions": {"type": "number", "optional": false}, "deletions": {"type": "number", "optional": false}, "changed_files": {"type": "number", "optional": false}}, "GithubIssueAssignee": {"id": {"type": "number", "optional": false}, "login": {"type": "string", "optional": false}, "avatar_url": {"type": "string", "optional": false}, "html_url": {"type": "string", "optional": false}}, "GithubTeamRef": {"id": {"type": "number", "optional": false}, "name": {"type": "string", "optional": false}}, "GithubPullRequestReview": {"id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "user": {"type": {"$ref": "GithubIssueCreator"}, "optional": false}, "body": {"type": "string", "optional": false}, "state": {"type": "string", "optional": false}, "html_url": {"type": "string", "optional": false}, "pull_request_url": {"type": "string", "optional": false}, "submitted_at": {"type": "string", "optional": false}, "commit_id": {"type": "string", "optional": false}, "author_association": {"type": "string", "optional": false}, "_links": {"type": "object", "optional": false}}, "GithubDeleteRepositoryOutput": {"success": {"type": "boolean", "optional": false}, "message": {"type": "string", "optional": false}}, "GithubPullRequestCommentList": {"comments": {"type": {"$ref": "GithubPullRequestComment"}, "optional": false, "array": true}}, "GithubPullRequestFileList": {"files": {"type": {"$ref": "GithubPullRequestFile"}, "optional": false, "array": true}}, "GithubPullRequestFile": {"sha": {"type": "string", "optional": false}, "filename": {"type": "string", "optional": false}, "status": {"type": "string", "optional": false}, "additions": {"type": "number", "optional": false}, "deletions": {"type": "number", "optional": false}, "changes": {"type": "number", "optional": false}, "blob_url": {"type": "string", "optional": false}, "raw_url": {"type": "string", "optional": false}, "contents_url": {"type": "string", "optional": false}, "patch": {"type": "string", "optional": false}}, "GithubCombinedStatus": {"state": {"type": "string", "optional": false}, "sha": {"type": "string", "optional": false}, "total_count": {"type": "number", "optional": false}, "statuses": {"type": {"$ref": "GithubStatus"}, "optional": false, "array": true}, "repository": {"type": {"$ref": "GithubRepositoryForGithubCombinedStatus"}, "optional": false}, "commit_url": {"type": "string", "optional": false}}, "GithubStatus": {"url": {"type": "string", "optional": false}, "id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "state": {"type": "string", "optional": false}, "context": {"type": "string", "optional": false}, "description": {"type": "string", "optional": false}, "target_url": {"type": "string", "optional": false}, "created_at": {"type": "string", "optional": false}, "updated_at": {"type": "string", "optional": false}}, "GithubRepositoryForGithubCombinedStatus": {"id": {"type": "number", "optional": false}, "node_id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "full_name": {"type": "string", "optional": false}, "private": {"type": "boolean", "optional": false}, "owner": {"type": {"$ref": "GithubIssueCreator"}, "optional": false}, "html_url": {"type": "string", "optional": false}, "description": {"type": "string", "optional": false}, "fork": {"type": "boolean", "optional": false}, "url": {"type": "string", "optional": false}, "forks_url": {"type": "string", "optional": false}, "keys_url": {"type": "string", "optional": false}, "collaborators_url": {"type": "string", "optional": false}, "teams_url": {"type": "string", "optional": false}, "hooks_url": {"type": "string", "optional": false}, "issue_events_url": {"type": "string", "optional": false}, "events_url": {"type": "string", "optional": false}, "assignees_url": {"type": "string", "optional": false}, "branches_url": {"type": "string", "optional": false}, "tags_url": {"type": "string", "optional": false}, "blobs_url": {"type": "string", "optional": false}, "git_tags_url": {"type": "string", "optional": false}, "git_refs_url": {"type": "string", "optional": false}, "trees_url": {"type": "string", "optional": false}, "statuses_url": {"type": "string", "optional": false}, "languages_url": {"type": "string", "optional": false}, "stargazers_url": {"type": "string", "optional": false}, "contributors_url": {"type": "string", "optional": false}, "subscribers_url": {"type": "string", "optional": false}, "subscription_url": {"type": "string", "optional": false}, "commits_url": {"type": "string", "optional": false}, "git_commits_url": {"type": "string", "optional": false}, "comments_url": {"type": "string", "optional": false}, "issue_comment_url": {"type": "string", "optional": false}, "contents_url": {"type": "string", "optional": false}, "compare_url": {"type": "string", "optional": false}, "merges_url": {"type": "string", "optional": false}, "archive_url": {"type": "string", "optional": false}, "downloads_url": {"type": "string", "optional": false}, "issues_url": {"type": "string", "optional": false}, "pulls_url": {"type": "string", "optional": false}, "milestones_url": {"type": "string", "optional": false}, "notifications_url": {"type": "string", "optional": false}, "labels_url": {"type": "string", "optional": false}, "releases_url": {"type": "string", "optional": false}, "deployments_url": {"type": "string", "optional": false}}, "GithubBranchList": {"branches": {"type": {"$ref": "GithubBranch"}, "optional": false, "array": true}}, "GithubBranch": {"name": {"type": "string", "optional": false}, "commit": {"type": "object", "optional": false}, "protected": {"type": "boolean", "optional": false}, "protection": {"type": "object", "optional": true}, "protection_url": {"type": "string", "optional": true}}, "GithubIssueList": {"issues": {"type": {"$ref": "GithubIssue"}, "optional": false, "array": true}}, "GithubPullRequestList": {"pull_requests": {"type": {"$ref": "GithubPullRequest"}, "optional": false, "array": true}}, "GithubRepositoryList": {"note": {"type": "union", "optional": false, "union": ["string", null]}, "repositories": {"type": {"$ref": "GithubRepository"}, "optional": false, "array": true}}, "GithubMergeResult": {"sha": {"type": "string", "optional": false}, "merged": {"type": "boolean", "optional": false}, "message": {"type": "string", "optional": false}}, "GithubBranchUpdateResult": {"message": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}}, "GithubWriteFileActionResult": {"url": {"type": "string", "optional": false}, "status": {"type": "string", "optional": false}, "sha": {"type": "string", "optional": false}}, "SlackReactionOutput": {"ok": {"type": "boolean", "optional": false}, "error": {"type": "string", "optional": true}}, "SlackDeleteMessageOutput": {"ok": {"type": "boolean", "optional": false}, "channel": {"type": "string", "optional": true}, "ts": {"type": "string", "optional": true}, "error": {"type": "string", "optional": true}}, "SlackMessageList": {"ok": {"type": "boolean", "optional": false}, "messages": {"type": {"$ref": "SlackMessage"}, "optional": false, "array": true}, "has_more": {"type": "boolean", "optional": true}, "pin_count": {"type": "number", "optional": true}, "channel_actions_ts": {"type": "string", "optional": true}, "channel_actions_count": {"type": "number", "optional": true}, "response_metadata": {"type": {"$ref": "SlackResponseMetadata"}, "optional": true}, "error": {"type": "string", "optional": true}}, "SlackMessage": {"type": {"type": "string", "optional": false}, "subtype": {"type": "string", "optional": true}, "ts": {"type": "string", "optional": false}, "user": {"type": "string", "optional": true}, "text": {"type": "string", "optional": false}, "thread_ts": {"type": "string", "optional": true}, "reply_count": {"type": "number", "optional": true}, "blocks": {"type": {"$ref": "<PERSON><PERSON>ck<PERSON>lock"}, "optional": true, "array": true}, "attachments": {"type": {"$ref": "SlackAttachment"}, "optional": true, "array": true}, "files": {"type": {"$ref": "SlackFile"}, "optional": true, "array": true}, "reactions": {"type": {"$ref": "SlackReaction"}, "optional": true, "array": true}, "parent_user_id": {"type": "string", "optional": true}, "edited": {"type": {"$ref": "<PERSON><PERSON>ckEdited"}, "optional": true}, "bot_id": {"type": "string", "optional": true}, "icons": {"type": "object", "optional": true}, "team": {"type": "string", "optional": true}, "app_id": {"type": "string", "optional": true}, "client_msg_id": {"type": "string", "optional": true}}, "SlackBlock": {"type": {"type": "string", "optional": false}, "block_id": {"type": "string", "optional": true}, "text": {"type": "object", "optional": true}, "elements": {"type": "object", "optional": true, "array": true}, "fields": {"type": "object", "optional": true, "array": true}, "accessory": {"type": "object", "optional": true}}, "SlackAttachment": {"id": {"type": "number", "optional": true}, "fallback": {"type": "string", "optional": true}, "color": {"type": "string", "optional": true}, "pretext": {"type": "string", "optional": true}, "author_name": {"type": "string", "optional": true}, "author_link": {"type": "string", "optional": true}, "author_icon": {"type": "string", "optional": true}, "title": {"type": "string", "optional": true}, "title_link": {"type": "string", "optional": true}, "text": {"type": "string", "optional": true}, "fields": {"type": "object", "optional": true, "array": true}, "image_url": {"type": "string", "optional": true}, "thumb_url": {"type": "string", "optional": true}, "footer": {"type": "string", "optional": true}, "footer_icon": {"type": "string", "optional": true}, "ts": {"type": "number", "optional": true}}, "SlackFile": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": true}, "filetype": {"type": "string", "optional": true}, "url_private": {"type": "string", "optional": true}, "url_private_download": {"type": "string", "optional": true}, "mimetype": {"type": "string", "optional": true}, "size": {"type": "number", "optional": true}, "title": {"type": "string", "optional": true}, "created": {"type": "number", "optional": true}, "timestamp": {"type": "number", "optional": true}, "user": {"type": "string", "optional": true}, "editable": {"type": "boolean", "optional": true}, "mode": {"type": "string", "optional": true}, "is_external": {"type": "boolean", "optional": true}, "external_type": {"type": "string", "optional": true}, "permalink": {"type": "string", "optional": true}, "preview": {"type": "string", "optional": true}, "accessible": {"type": {"$ref": "UrlAccessibleFile"}, "optional": true}}, "UrlAccessibleFile": {"url": {"type": "string", "optional": false}, "authentication": {"type": {"$ref": "UrlAuthentication"}, "optional": false}}, "UrlAuthentication": {"providerKey": {"type": "string", "optional": false}, "connectionId": {"type": "string", "optional": false}}, "SlackReaction": {"name": {"type": "string", "optional": false}, "count": {"type": "number", "optional": false}, "users": {"type": "string", "optional": false, "array": true}}, "SlackEdited": {"user": {"type": "string", "optional": false}, "ts": {"type": "string", "optional": false}}, "SlackResponseMetadata": {"next_cursor": {"type": "string", "optional": true}}, "SlackPermalinkOutput": {"ok": {"type": "boolean", "optional": false}, "permalink": {"type": "string", "optional": true}, "channel": {"type": "string", "optional": true}, "error": {"type": "string", "optional": true}}, "SlackUserInfo": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "is_bot": {"type": "boolean", "optional": false}, "is_admin": {"type": "boolean", "optional": true}, "is_owner": {"type": "boolean", "optional": true}, "tz": {"type": "string", "optional": true}, "profile": {"type": {"$ref": "SlackUserProfile"}, "optional": true}}, "SlackUserProfile": {"real_name": {"type": "string", "optional": true}, "display_name": {"type": "string", "optional": true}, "email": {"type": "string", "optional": true}, "image_original": {"type": "string", "optional": true}, "image_512": {"type": "string", "optional": true}}, "SlackConversationsList": {"ok": {"type": "boolean", "optional": false}, "channels": {"type": {"$ref": "SlackConversation"}, "optional": false, "array": true}, "response_metadata": {"type": {"$ref": "SlackResponseMetadata"}, "optional": true}, "error": {"type": "string", "optional": true}}, "SlackConversation": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": true}, "is_channel": {"type": "boolean", "optional": true}, "is_group": {"type": "boolean", "optional": true}, "is_im": {"type": "boolean", "optional": true}, "is_mpim": {"type": "boolean", "optional": true}, "is_private": {"type": "boolean", "optional": true}, "is_member": {"type": "boolean", "optional": true}, "user": {"type": "string", "optional": true}, "num_members": {"type": "number", "optional": true}}, "SlackSearchResultList": {"ok": {"type": "boolean", "optional": false}, "query": {"type": "string", "optional": false}, "messages": {"type": "object", "optional": false}, "error": {"type": "string", "optional": true}}, "SlackSendMessageOutput": {"ok": {"type": "boolean", "optional": false}, "ts": {"type": "string", "optional": false}, "channel": {"type": "string", "optional": false}, "message_text": {"type": "string", "optional": true}}, "SlackUpdateMessageOutput": {"ok": {"type": "boolean", "optional": false}, "channel": {"type": "string", "optional": true}, "ts": {"type": "string", "optional": true}, "text": {"type": "string", "optional": true}, "error": {"type": "string", "optional": true}}, "GoogleCalendarEvent": {"id": {"type": "string", "optional": false}, "kind": {"type": "string", "optional": false}, "etag": {"type": "string", "optional": false}, "status": {"type": "string", "optional": false}, "htmlLink": {"type": "string", "optional": false}, "created": {"type": "string", "optional": false}, "updated": {"type": "string", "optional": false}, "summary": {"type": "string", "optional": false, "description": "Event title / name."}, "description": {"type": "string", "optional": true, "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."}, "location": {"type": "string", "optional": true, "description": "Free form text."}, "creator": {"type": "any", "optional": false}, "organizer": {"type": "any", "optional": false}, "start": {"type": "any", "optional": false, "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."}, "end": {"type": "any", "optional": false, "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."}, "iCalUID": {"type": "string", "optional": false}, "sequence": {"type": "number", "optional": true}, "eventType": {"type": "string", "optional": false}, "attendees": {"type": "any", "optional": true, "description": "A list of attendee email addresses.", "array": true}, "recurrence": {"type": "string", "optional": true, "array": true}, "recurringEventId": {"type": "string", "optional": true}, "reminders": {"type": "any", "optional": false}, "hangoutLink": {"type": "string", "optional": true}, "conferenceData": {"type": "any", "optional": true}, "anyoneCanAddSelf": {"type": "boolean", "optional": true}, "guestsCanInviteOthers": {"type": "boolean", "optional": true}, "guestsCanSeeOtherGuests": {"type": "boolean", "optional": true}, "guestsCanModify": {"type": "boolean", "optional": true}, "privateCopy": {"type": "boolean", "optional": true}, "transparency": {"type": "string", "optional": true}, "visibility": {"type": "string", "optional": true}, "colorId": {"type": "string", "optional": true}, "attachments": {"type": "any", "optional": true, "array": true}}, "GoogleCalendarEventDeleteOutput": {"event": {"type": {"$ref": "GoogleCalendarEvent"}, "optional": false}, "deletedAt": {"type": "string", "optional": false}}, "GoogleCalendarList": {"calendars": {"type": "any", "optional": false, "array": true}, "nextPageToken": {"type": "string", "optional": true}}, "GoogleCalendarEventList": {"items": {"type": "any", "optional": false, "array": true}, "nextPageToken": {"type": "string", "optional": true}, "timeZone": {"type": "string", "optional": false, "description": "Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."}}, "GmailDraftOutput": {"id": {"type": "string", "optional": false}, "threadId": {"type": "union", "optional": false, "union": ["string", null]}}, "GmailReplyDraftOutput": {"id": {"type": "string", "optional": false}, "threadId": {"type": "union", "optional": false, "union": ["string", null]}}, "GmailDeleteMessageOutput": {"success": {"type": "boolean", "optional": false}}, "GmailMessage": {"id": {"type": "string", "optional": false}, "threadId": {"type": "string", "optional": false}, "labelIds": {"type": "string", "optional": true, "array": true}, "snippet": {"type": "string", "optional": true}, "payload": {"type": "object", "optional": true}, "sizeEstimate": {"type": "number", "optional": true}, "historyId": {"type": "string", "optional": true}, "internalDate": {"type": "string", "optional": true}, "headers": {"type": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "optional": true, "array": true}, "body": {"type": "string", "optional": true}, "mimeType": {"type": "string", "optional": true}, "filename": {"type": "string", "optional": true}, "attachments": {"type": {"$ref": "GmailAttachmentInfo"}, "optional": true, "array": true}}, "GmailHeader": {"name": {"type": "string", "optional": false}, "value": {"type": "string", "optional": false}}, "GmailAttachmentInfo": {"filename": {"type": "string", "optional": false}, "mimeType": {"type": "string", "optional": false}, "size": {"type": "number", "optional": false}, "attachmentId": {"type": "string", "optional": true}}, "GmailMessageList": {"messages": {"type": {"$ref": "GmailBasicMessageDetails"}, "optional": false, "array": true}, "nextPageToken": {"type": "string", "optional": true}}, "GmailBasicMessageDetails": {"id": {"type": "string", "optional": false}, "threadId": {"type": "string", "optional": false}, "labelIds": {"type": "string", "optional": true, "array": true}, "snippet": {"type": "string", "optional": true}, "subject": {"type": "string", "optional": true}, "date": {"type": "string", "optional": true}}, "GmailSendEmailOutput": {"id": {"type": "string", "optional": false}, "threadId": {"type": "string", "optional": false}, "labelIds": {"type": "string", "optional": true, "array": true}}, "DropboxEntry": {".tag": {"type": "string", "optional": false}, "id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "path_display": {"type": "string", "optional": false}, "path_lower": {"type": "string", "optional": false}, "client_modified": {"type": "string", "optional": true}, "server_modified": {"type": "string", "optional": true}, "rev": {"type": "string", "optional": true}, "size": {"type": "number", "optional": true}, "is_downloadable": {"type": "boolean", "optional": true}, "content_hash": {"type": "string", "optional": true}}, "DropboxFolder": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "path_display": {"type": "string", "optional": false}, "path_lower": {"type": "string", "optional": false}}, "DropboxDeleteResult": {"metadata": {"type": {"$ref": "DropboxEntry"}, "optional": false}}, "DropboxFile": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "path_display": {"type": "string", "optional": false}, "path_lower": {"type": "string", "optional": false}, "size": {"type": "number", "optional": false}, "content_hash": {"type": "string", "optional": true}, "server_modified": {"type": "string", "optional": false}, "content": {"type": "string", "optional": true}, "download_url": {"type": "string", "optional": true}}, "DropboxFileList": {"entries": {"type": {"$ref": "DropboxEntry"}, "optional": false, "array": true}, "cursor": {"type": "string", "optional": true}, "has_more": {"type": "boolean", "optional": false}}, "DropboxSearchResult": {"matches": {"type": {"$ref": "DropboxSearchMatch"}, "optional": false, "array": true}, "more": {"type": "boolean", "optional": false}, "start": {"type": "number", "optional": false}}, "DropboxSearchMatch": {"metadata": {"type": {"$ref": "DropboxEntry"}, "optional": false}, "match_type": {"type": "string", "optional": false}}, "NotionDatabase": {"object": {"type": "string", "optional": false}, "id": {"type": "string", "optional": false}, "created_time": {"type": "string", "optional": true}, "last_edited_time": {"type": "string", "optional": true}, "created_by": {"type": {"$ref": "NotionUserReference"}, "optional": true}, "last_edited_by": {"type": {"$ref": "NotionUserReference"}, "optional": true}, "icon": {"type": "union", "optional": true, "union": ["NotionIcon", null]}, "cover": {"type": "union", "optional": true, "union": ["NotionCover", null]}, "parent": {"type": {"$ref": "NotionParentReference"}, "optional": true}, "archived": {"type": "boolean", "optional": true}, "in_trash": {"type": "boolean", "optional": true}, "properties": {"type": {"$ref": "NotionProperties"}, "optional": false}, "url": {"type": "string", "optional": true}, "public_url": {"type": "union", "optional": true, "union": ["string", null]}, "title": {"type": {"$ref": "NotionRichText"}, "optional": true, "array": true}, "description": {"type": {"$ref": "NotionRichText"}, "optional": true, "array": true}, "is_inline": {"type": "boolean", "optional": true}, "request_id": {"type": "string", "optional": true}}, "NotionUserReference": {"object": {"type": "string", "optional": false}, "id": {"type": "string", "optional": false}}, "NotionParentReference": {"type": {"type": "string", "optional": false}, "workspace": {"type": "boolean", "optional": true}, "page_id": {"type": "string", "optional": true}, "database_id": {"type": "string", "optional": true}}, "NotionProperties": {"title": {"type": {"$ref": "NotionTitleProperty"}, "optional": true}}, "NotionTitleProperty": {"id": {"type": "string", "optional": true}, "type": {"type": "string", "optional": true}, "title": {"type": {"$ref": "NotionRichText"}, "optional": true, "array": true}}, "NotionRichText": {"type": {"type": "string", "optional": true}, "text": {"type": {"$ref": "NotionRichTextContent"}, "optional": true}, "annotations": {"type": {"$ref": "NotionRichTextAnnotations"}, "optional": true}, "plain_text": {"type": "string", "optional": true}, "href": {"type": "union", "optional": true, "union": ["string", null]}}, "NotionRichTextContent": {"content": {"type": "string", "optional": true}, "link": {"type": "union", "optional": true, "union": ["Record<string, any>", null]}}, "NotionRichTextAnnotations": {"bold": {"type": "boolean", "optional": true}, "italic": {"type": "boolean", "optional": true}, "strikethrough": {"type": "boolean", "optional": true}, "underline": {"type": "boolean", "optional": true}, "code": {"type": "boolean", "optional": true}, "color": {"type": "string", "optional": true}}, "NotionPageOrDatabase": {"object": {"type": "string", "optional": false}, "id": {"type": "string", "optional": false}, "created_time": {"type": "string", "optional": true}, "last_edited_time": {"type": "string", "optional": true}, "created_by": {"type": {"$ref": "NotionUserReference"}, "optional": true}, "last_edited_by": {"type": {"$ref": "NotionUserReference"}, "optional": true}, "cover": {"type": "union", "optional": true, "union": ["NotionCover", null]}, "icon": {"type": "union", "optional": true, "union": ["NotionIcon", null]}, "parent": {"type": {"$ref": "NotionParentReference"}, "optional": true}, "archived": {"type": "boolean", "optional": true}, "in_trash": {"type": "boolean", "optional": true}, "properties": {"type": {"$ref": "NotionProperties"}, "optional": true}, "url": {"type": "string", "optional": true}, "public_url": {"type": "union", "optional": true, "union": ["string", null]}, "title": {"type": {"$ref": "NotionRichText"}, "optional": true, "array": true}, "description": {"type": {"$ref": "NotionRichText"}, "optional": true, "array": true}, "is_inline": {"type": "boolean", "optional": true}, "request_id": {"type": "string", "optional": true}}, "NotionQueryDatabaseOutput": {"object": {"type": "string", "optional": false}, "results": {"type": {"$ref": "NotionPageOrDatabase"}, "optional": false, "array": true}, "next_cursor": {"type": "union", "optional": true, "union": ["string", null]}, "has_more": {"type": "boolean", "optional": false}, "type": {"type": "string", "optional": true}, "page": {"type": {"$ref": "NotionGenericObjectPlaceholder"}, "optional": true}, "request_id": {"type": "string", "optional": true}}, "NotionGenericObjectPlaceholder": {"_placeholder": {"type": "string", "optional": true}}, "NotionSearchOutput": {"object": {"type": "string", "optional": false}, "results": {"type": {"$ref": "NotionPageOrDatabase"}, "optional": false, "array": true}, "next_cursor": {"type": "union", "optional": true, "union": ["string", null]}, "has_more": {"type": "boolean", "optional": false}, "type": {"type": "string", "optional": true}, "page_or_database": {"type": {"$ref": "NotionGenericObjectPlaceholder"}, "optional": true}, "request_id": {"type": "string", "optional": true}}, "GoogleDocsDocument": {"documentId": {"type": "string", "optional": true}, "title": {"type": "string", "optional": true}, "body": {"type": "object", "optional": true}, "headers": {"type": "object", "optional": true}, "footers": {"type": "object", "optional": true}, "footnotes": {"type": "object", "optional": true}, "documentStyle": {"type": "object", "optional": true}, "namedStyles": {"type": "object", "optional": true}, "revisionId": {"type": "string", "optional": true}, "suggestionsViewMode": {"type": "string", "optional": true}, "inlineObjects": {"type": "object", "optional": true}, "positionedObjects": {"type": "object", "optional": true}, "tabs": {"type": "object", "optional": true, "array": true}}, "GoogleDocsUpdateDocumentOutput": {"documentId": {"type": "string", "optional": false}, "replies": {"type": "object", "optional": true, "array": true}, "writeControl": {"type": "object", "optional": true}}, "LinearIssue": {"id": {"type": "string", "optional": false}, "title": {"type": "string", "optional": false}, "description": {"type": "string", "optional": true}, "number": {"type": "number", "optional": false}, "priority": {"type": "number", "optional": false}, "url": {"type": "string", "optional": false}, "createdAt": {"type": "string", "optional": false}, "updatedAt": {"type": "string", "optional": false}, "state": {"type": {"$ref": "LinearState"}, "optional": false}, "assignee": {"type": {"$ref": "LinearUser"}, "optional": true}, "team": {"type": {"$ref": "LinearTeamBasic"}, "optional": false}, "project": {"type": {"$ref": "LinearProjectBasic"}, "optional": true}, "labels": {"type": {"$ref": "LinearLabel"}, "optional": true, "array": true}}, "LinearState": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "color": {"type": "string", "optional": false}, "type": {"type": "string", "optional": false}}, "LinearUser": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "email": {"type": "string", "optional": false}, "displayName": {"type": "string", "optional": true}, "avatarUrl": {"type": "union", "optional": true, "union": ["string", null]}}, "LinearTeamBasic": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "key": {"type": "string", "optional": false}}, "LinearProjectBasic": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "icon": {"type": "string", "optional": true}, "color": {"type": "string", "optional": true}}, "LinearLabel": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "color": {"type": "string", "optional": false}}, "LinearProject": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "description": {"type": "string", "optional": true}, "url": {"type": "string", "optional": true}, "color": {"type": "string", "optional": true}, "state": {"type": "string", "optional": false}, "lead": {"type": {"$ref": "LinearUserBasic"}, "optional": true}, "teams": {"type": {"$ref": "LinearTeamBasic"}, "optional": false, "array": true}, "createdAt": {"type": "string", "optional": false}, "updatedAt": {"type": "string", "optional": false}}, "LinearUserBasic": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "email": {"type": "string", "optional": true}}, "LinearDeleteIssueOutput": {"success": {"type": "boolean", "optional": false}, "issueId": {"type": "string", "optional": false}}, "ModelResponse": {"models": {"type": {"$ref": "Model"}, "optional": false, "array": true}}, "Model": {"name": {"type": "string", "optional": false}}, "LinearTeam": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "key": {"type": "string", "optional": false}, "description": {"type": "union", "optional": true, "union": ["string", null]}, "color": {"type": "string", "optional": true}, "private": {"type": "boolean", "optional": true}, "createdAt": {"type": "string", "optional": false}, "updatedAt": {"type": "string", "optional": false}, "members": {"type": {"$ref": "LinearUser"}, "optional": false, "array": true}}, "LinearIssueList": {"issues": {"type": {"$ref": "LinearIssue"}, "optional": false, "array": true}, "pageInfo": {"type": {"$ref": "PageInfo"}, "optional": true}}, "PageInfo": {"hasNextPage": {"type": "boolean", "optional": false}, "endCursor": {"type": "string", "optional": true}}, "LinearProjectList": {"projects": {"type": {"$ref": "LinearProject"}, "optional": false, "array": true}, "pageInfo": {"type": {"$ref": "PageInfo"}, "optional": true}}, "LinearTeamList": {"teams": {"type": {"$ref": "LinearTeam"}, "optional": false, "array": true}, "pageInfo": {"type": {"$ref": "PageInfo"}, "optional": true}}, "GoogleSheetCreateOutput": {"id": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}, "title": {"type": "string", "optional": false}}, "Spreadsheet": {"spreadsheetId": {"type": "string", "optional": false}, "properties": {"type": "object", "optional": false}, "sheets": {"type": "object", "optional": false, "array": true}, "namedRanges": {"type": "object", "optional": false, "array": true}, "spreadsheetUrl": {"type": "string", "optional": false}, "developerMetadata": {"type": "object", "optional": false, "array": true}, "dataSources": {"type": "object", "optional": false, "array": true}, "dataSourceSchedules": {"type": "object", "optional": false, "array": true}}, "GoogleSheetUpdateOutput": {"spreadsheetId": {"type": "string", "optional": false}, "updatedRange": {"type": "string", "optional": false}, "updatedRows": {"type": "number", "optional": false}, "updatedColumns": {"type": "number", "optional": false}, "updatedCells": {"type": "number", "optional": false}}, "Anonymous_googledrive_action_fetchdocument_output": {"output": {"type": "string", "optional": false}}, "JSONDocument": {"documentId": {"type": "string", "optional": false}, "title": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}, "tabs": {"type": "object", "optional": false, "array": true}, "revisionId": {"type": "string", "optional": false}, "suggestionsViewMode": {"type": "union", "optional": false, "union": ["DEFAULT_FOR_CURRENT_ACCESS", "SUGGESTIONS_INLINE", "PREVIEW_SUGGESTIONS_ACCEPTED", "PREVIEW_WITHOUT_SUGGESTIONS"]}, "body": {"type": "object", "optional": false}, "headers": {"type": "object", "optional": false}, "footers": {"type": "object", "optional": false}, "footnotes": {"type": "object", "optional": false}, "documentStyle": {"type": "object", "optional": false}, "suggestedDocumentStyleChanges": {"type": "object", "optional": false}, "namedStyles": {"type": "object", "optional": false}, "suggestedNamedStylesChanges": {"type": "object", "optional": false}, "lists": {"type": "object", "optional": false}, "namedRanges": {"type": "object", "optional": false}, "inlineObjects": {"type": "object", "optional": false}, "positionedObjects": {"type": "object", "optional": false}}, "JSONSpreadsheet": {"spreadsheetId": {"type": "string", "optional": false}, "properties": {"type": "object", "optional": false}, "sheets": {"type": "object", "optional": false, "array": true}, "namedRanges": {"type": "object", "optional": false, "array": true}, "spreadsheetUrl": {"type": "string", "optional": false}, "developerMetadata": {"type": "object", "optional": false, "array": true}, "dataSources": {"type": "object", "optional": false, "array": true}, "dataSourceSchedules": {"type": "object", "optional": false, "array": true}}, "FolderContent": {"files": {"type": {"$ref": "GoogleDocument"}, "optional": false, "array": true}, "folders": {"type": {"$ref": "GoogleDocument"}, "optional": false, "array": true}, "cursor": {"type": "string", "optional": true}}, "GoogleDocument": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "mimeType": {"type": "string", "optional": false}, "parents": {"type": "string", "optional": true, "array": true}, "modifiedTime": {"type": "string", "optional": true}, "createdTime": {"type": "string", "optional": true}, "webViewLink": {"type": "string", "optional": true}, "kind": {"type": "string", "optional": true}}, "GoogleDriveDocumentList": {"documents": {"type": {"$ref": "GoogleDriveDocument"}, "optional": false, "array": true}, "nextPageToken": {"type": "string", "optional": true}}, "GoogleDriveDocument": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "mimeType": {"type": "string", "optional": false}, "webViewLink": {"type": "string", "optional": false}, "modifiedTime": {"type": "string", "optional": false}, "createdTime": {"type": "string", "optional": false}, "parents": {"type": "string", "optional": false, "array": true}, "size": {"type": "string", "optional": false}}, "GoogleDriveFolderList": {"folders": {"type": {"$ref": "GoogleDriveFolder"}, "optional": false, "array": true}, "nextPageToken": {"type": "string", "optional": true}}, "GoogleDriveFolder": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "mimeType": {"type": "string", "optional": false}, "webViewLink": {"type": "string", "optional": false}, "modifiedTime": {"type": "string", "optional": false}, "createdTime": {"type": "string", "optional": false}, "parents": {"type": "string", "optional": false, "array": true}}, "XSocialUserProfile": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "username": {"type": "string", "optional": false}, "profile_image_url": {"type": "string", "optional": false}, "description": {"type": "string", "optional": false}, "location": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}, "protected": {"type": "boolean", "optional": false}, "verified": {"type": "boolean", "optional": false}, "followers_count": {"type": "number", "optional": false}, "following_count": {"type": "number", "optional": false}, "tweet_count": {"type": "number", "optional": false}, "listed_count": {"type": "number", "optional": false}}, "XSocialPostOutput": {"id": {"type": "string", "optional": false}, "text": {"type": "string", "optional": false}, "created_at": {"type": "string", "optional": false}}, "LinkedInUserProfile": {"sub": {"type": "string", "optional": false}, "email_verified": {"type": "boolean", "optional": false}, "name": {"type": "string", "optional": false}, "locale": {"type": "object", "optional": false}, "given_name": {"type": "string", "optional": false}, "family_name": {"type": "string", "optional": false}, "email": {"type": "string", "optional": false}, "picture": {"type": "string", "optional": false}}, "LinkedInPostOutput": {"id": {"type": "string", "optional": false}}}, "syncOutputs": [{"provider": "slack", "sync": "messages", "model": "SlackSyncMessage", "description": "Syncs messages from all channels the user can access, including replies."}, {"provider": "google-calendar", "sync": "events-fork", "model": "GoogleCalendarEvent", "description": "Syncs Google Calendar events from the user's primary calendar"}, {"provider": "google-mail", "sync": "emails-fork", "model": "GmailEmail", "description": "Fetches a list of emails from Gmail. Defaults to 1-day backfill,"}, {"provider": "dropbox", "sync": "files-fork", "model": "DropboxFile", "description": "Fetches files metadata from Dropbox."}, {"provider": "google-drive", "sync": "documents-fork", "model": "Document", "description": "Fetches documents metadata from Google Drive."}], "syncOutputModelsDictionary": {"SlackSyncMessage": {"id": {"type": "string", "optional": false}, "channel_id": {"type": "string", "optional": false}, "message": {"type": {"$ref": "SlackMessage"}, "optional": false}}, "SlackMessage": {"type": {"type": "string", "optional": false}, "subtype": {"type": "string", "optional": true}, "ts": {"type": "string", "optional": false}, "user": {"type": "string", "optional": true}, "text": {"type": "string", "optional": false}, "thread_ts": {"type": "string", "optional": true}, "reply_count": {"type": "number", "optional": true}, "blocks": {"type": {"$ref": "<PERSON><PERSON>ck<PERSON>lock"}, "optional": true, "array": true}, "attachments": {"type": {"$ref": "SlackAttachment"}, "optional": true, "array": true}, "files": {"type": {"$ref": "SlackFile"}, "optional": true, "array": true}, "reactions": {"type": {"$ref": "SlackReaction"}, "optional": true, "array": true}, "parent_user_id": {"type": "string", "optional": true}, "edited": {"type": {"$ref": "<PERSON><PERSON>ckEdited"}, "optional": true}, "bot_id": {"type": "string", "optional": true}, "icons": {"type": "object", "optional": true}, "team": {"type": "string", "optional": true}, "app_id": {"type": "string", "optional": true}, "client_msg_id": {"type": "string", "optional": true}}, "SlackBlock": {"type": {"type": "string", "optional": false}, "block_id": {"type": "string", "optional": true}, "text": {"type": "object", "optional": true}, "elements": {"type": "object", "optional": true, "array": true}, "fields": {"type": "object", "optional": true, "array": true}, "accessory": {"type": "object", "optional": true}}, "SlackAttachment": {"id": {"type": "number", "optional": true}, "fallback": {"type": "string", "optional": true}, "color": {"type": "string", "optional": true}, "pretext": {"type": "string", "optional": true}, "author_name": {"type": "string", "optional": true}, "author_link": {"type": "string", "optional": true}, "author_icon": {"type": "string", "optional": true}, "title": {"type": "string", "optional": true}, "title_link": {"type": "string", "optional": true}, "text": {"type": "string", "optional": true}, "fields": {"type": "object", "optional": true, "array": true}, "image_url": {"type": "string", "optional": true}, "thumb_url": {"type": "string", "optional": true}, "footer": {"type": "string", "optional": true}, "footer_icon": {"type": "string", "optional": true}, "ts": {"type": "number", "optional": true}}, "SlackFile": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": true}, "filetype": {"type": "string", "optional": true}, "url_private": {"type": "string", "optional": true}, "url_private_download": {"type": "string", "optional": true}, "mimetype": {"type": "string", "optional": true}, "size": {"type": "number", "optional": true}, "title": {"type": "string", "optional": true}, "created": {"type": "number", "optional": true}, "timestamp": {"type": "number", "optional": true}, "user": {"type": "string", "optional": true}, "editable": {"type": "boolean", "optional": true}, "mode": {"type": "string", "optional": true}, "is_external": {"type": "boolean", "optional": true}, "external_type": {"type": "string", "optional": true}, "permalink": {"type": "string", "optional": true}, "preview": {"type": "string", "optional": true}, "accessible": {"type": {"$ref": "UrlAccessibleFile"}, "optional": true}}, "UrlAccessibleFile": {"url": {"type": "string", "optional": false}, "authentication": {"type": {"$ref": "UrlAuthentication"}, "optional": false}}, "UrlAuthentication": {"providerKey": {"type": "string", "optional": false}, "connectionId": {"type": "string", "optional": false}}, "SlackReaction": {"name": {"type": "string", "optional": false}, "count": {"type": "number", "optional": false}, "users": {"type": "string", "optional": false, "array": true}}, "SlackEdited": {"user": {"type": "string", "optional": false}, "ts": {"type": "string", "optional": false}}, "GoogleCalendarEvent": {"id": {"type": "string", "optional": false}, "kind": {"type": "string", "optional": false}, "etag": {"type": "string", "optional": false}, "status": {"type": "string", "optional": false}, "htmlLink": {"type": "string", "optional": false}, "created": {"type": "string", "optional": false}, "updated": {"type": "string", "optional": false}, "summary": {"type": "string", "optional": false}, "description": {"type": "string", "optional": true}, "location": {"type": "string", "optional": true}, "creator": {"type": "any", "optional": false}, "organizer": {"type": "any", "optional": false}, "start": {"type": "any", "optional": false}, "end": {"type": "any", "optional": false}, "iCalUID": {"type": "string", "optional": false}, "sequence": {"type": "number", "optional": true}, "eventType": {"type": "string", "optional": false}, "attendees": {"type": "any", "optional": true, "array": true}, "recurrence": {"type": "string", "optional": true, "array": true}, "recurringEventId": {"type": "string", "optional": true}, "reminders": {"type": "any", "optional": false}, "hangoutLink": {"type": "string", "optional": true}, "conferenceData": {"type": "any", "optional": true}, "anyoneCanAddSelf": {"type": "boolean", "optional": true}, "guestsCanInviteOthers": {"type": "boolean", "optional": true}, "guestsCanSeeOtherGuests": {"type": "boolean", "optional": true}, "guestsCanModify": {"type": "boolean", "optional": true}, "privateCopy": {"type": "boolean", "optional": true}, "transparency": {"type": "string", "optional": true}, "visibility": {"type": "string", "optional": true}, "colorId": {"type": "string", "optional": true}, "attachments": {"type": "any", "optional": true, "array": true}}, "GmailEmail": {"id": {"type": "string", "optional": false}, "sender": {"type": "string", "optional": false}, "recipients": {"type": "string", "optional": false}, "date": {"type": "string", "optional": false}, "subject": {"type": "string", "optional": false}, "body": {"type": "string", "optional": false}, "attachments": {"type": {"$ref": "Attachments"}, "optional": false, "array": true}, "threadId": {"type": "string", "optional": false}, "isDraft": {"type": "boolean", "optional": false}, "labels": {"type": "string", "optional": false, "array": true}, "snippet": {"type": "string", "optional": false}, "cc": {"type": "string", "optional": false}, "bcc": {"type": "string", "optional": false}, "messageId": {"type": "string", "optional": false}, "inReplyTo": {"type": "string", "optional": false}, "references": {"type": "string", "optional": false}}, "Attachments": {"filename": {"type": "string", "optional": false}, "mimeType": {"type": "string", "optional": false}, "size": {"type": "number", "optional": false}, "attachmentId": {"type": "string", "optional": false}}, "DropboxFile": {"id": {"type": "string", "optional": false}, "name": {"type": "string", "optional": false}, "path_display": {"type": "string", "optional": false}, "path_lower": {"type": "string", "optional": false}, "size": {"type": "number", "optional": false}, "content_hash": {"type": "string", "optional": true}, "server_modified": {"type": "string", "optional": false}, "content": {"type": "string", "optional": true}, "download_url": {"type": "string", "optional": true}}, "Document": {"id": {"type": "string", "optional": false}, "url": {"type": "string", "optional": false}, "mimeType": {"type": "string", "optional": false}, "title": {"type": "string", "optional": false}, "updatedAt": {"type": "string", "optional": false}}}}