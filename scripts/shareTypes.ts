#!/usr/bin/env node
// This script copies types from src/types/index.ts to Netlify functions shared folder

import fs from 'fs-extra';
import { resolve } from 'path';
import { fileURLToPath } from 'url';

/**
 * Copy the general types from src/types/index.ts to Netlify functions
 */
async function copyGeneralTypes(): Promise<void> {
  const basePath = resolve(fileURLToPath(import.meta.url), '..');
  const sourcePath = resolve(basePath, '../packages/ma-next/src/types/index.ts');
  const destPath = resolve(basePath, '../packages/ma-next/netlify/functions/_shared/types.ts');

  try {
    const sourceContent = await fs.readFile(sourcePath, 'utf8');
    const updatedContent = [
      '// AUTOGENERATED: copied here by scripts/shareTypes.ts, update src/types/index.ts and re-run if types need changing',
      '',
      sourceContent,
    ].join('\n');
    await fs.writeFile(destPath, updatedContent);
    console.log('✓ General types copied successfully');
  } catch (error) {
    console.error('Error copying general types file:', error);
    throw error;
  }
}

// Run directly
await copyGeneralTypes();

export { copyGeneralTypes };
